<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="常见问题" isPerch :isBack="true"> </c-navBar>
        </section>
        <section class="instruction-manual-list">
            <view class="manual-item" v-for="(item, index) in manualList" :key="item.id">
                <view class="item-header flex align-center justify-between" @click="toggleCollapse(index)">
                    <view class="flex align-center gap-24">
                        <view class="item-icon flex flex-center mf-font-20">{{ index + 1 }}</view>
                        <text class="item-title mf-font-32 mf-weight-bold">{{ item.name }}</text>
                    </view>
                    <view :class="item.isOpen ? 'icon-rotate' : ''">
                        <u-icon name="arrow-right" size="14"></u-icon>
                    </view>
                </view>
                <view class="item-content" v-if="item.isOpen">
                    <text class="content-text mf-font-28-h-44" style="color: #666">{{ item.remark }}</text>
                </view>
                <view class="divider"></view>
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            manualList: [
                {
                    id: 1,
                    name: "常见问题1",
                    remark: "常见问题1的回答",
                    isOpen: false,
                },
            ],
        };
    },
    methods: {
        toggleCollapse(index) {
            this.manualList[index].isOpen = !this.manualList[index].isOpen;
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .instruction-manual-list {
        padding: 26rpx 30rpx;
        background-color: #fff;
        margin-top: 12rpx;
        z-index: 11;
        border-radius: 20rpx 20rpx 0 0;
        .manual-item {
            .item-header {
                padding: 24rpx 0;
                .item-icon {
                    width: 36rpx;
                    height: 36rpx;
                    background-color: #ff5975;
                    border-radius: 5rpx;
                    color: #fff;
                    text-align: center;
                }
                .icon-rotate {
                    transform: rotate(90deg);
                    transition: transform 0.1s ease-in-out;
                }
            }

            .item-content {
                padding: 0 0 24rpx;
            }

            .divider {
                height: 1px;
                background-color: #f5f5f5;
            }
        }
    }
}
</style>
