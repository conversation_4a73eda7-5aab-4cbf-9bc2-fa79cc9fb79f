<template>
    <view class="view">
        <section class="bg">
            <image class="bg-img" src="/static/common/office-bg.png" mode="scaleToFill" />
        </section>
        <section class="navbar">
            <c-navBar isTran isSeat isPerch title="工作台" :isBack="false" color="#fff"> </c-navBar>
        </section>
        <section class="user-info">
            <image src="/static/common/user-card.png" mode="widthFix" />
            <view class="user-info-item">
                <view class="flex align-center gap-24">
                    <view class="user-avatar">
                        <u-image v-if="userInfo.avatar" :src="userInfo.avatar" width="128rpx" height="128rpx" radius="16rpx" mode="scaleToFill "></u-image>
                        <u-image v-else src="/static/mine/avatar.png" width="128rpx" height="128rpx" radius="16rpx" mode="scaleToFill "></u-image>
                        <view class="user-status mf-font-20" style="color: #fff">
                            {{ userInfo.online_status ? userInfo.online_status.name : "获取失败" }}
                        </view>
                    </view>
                    <view class="flex flex-col gap-30">
                        <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ userInfo.nickname }}</view>
                        <view class="mf-font-28" style="color: #ebb84c">{{ userInfo.role ? userInfo.role.role_name : "获取失败" }}</view>
                    </view>
                </view>
            </view>
        </section>
        <section class="actions">
            <scroll-view class="actions-scroll" scroll-y>
                <view class="actions-grid">
                    <view class="action-item flex align-center flex-col" v-for="(item, index) in actions" :key="index" @click.stop="handleActionClick(item)">
                        <image :src="item.icon" mode="widthFix" />
                        <view class="action-item-title mf-font-28" style="color: #1a1a1a">{{ item.title }}</view>
                    </view>
                </view>
            </scroll-view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            userInfo: {},
            role_view: [], // 角色权限
            actions: [
                {
                    icon: "/static/common/office-icon1.png",
                    title: "资金绩效",
                    path: "/pages/office/financialPerformance",
                    isLogin: true,
                    role_require: [],
                },
                {
                    icon: "/static/common/office-icon2.png",
                    title: "调度",
                    path: "/pages/office/dispatch",
                    isLogin: true,
                    role_require: [],
                },
                {
                    icon: "/static/common/office-icon3.png",
                    title: "资料库",
                    path: "/pages/office/database",
                    isLogin: true,
                    role_require: [],
                },
                {
                    icon: "/static/common/office-icon4.png",
                    title: "车辆管理",
                    path: "/pages/office/vehicleManagement",
                    isLogin: true,
                    role_require: [],
                },
                {
                    icon: "/static/common/office-icon5.png",
                    title: "请假",
                    path: "/pages/office/leaveApplication",
                    isLogin: true,
                    role_require: ["队长", "队员"],
                },
                {
                    icon: "/static/common/office-icon6.png",
                    title: "审批列表",
                    path: "/pages/office/applyList",
                    isLogin: true,
                    role_require: [],
                },
                {
                    icon: "/static/common/office-icon7.png",
                    title: "外出报备",
                    path: "/pages/office/reportingForGoingOut",
                    isLogin: true,
                    role_require: [],
                },
                {
                    icon: "/static/common/office-icon8.png",
                    title: "驻村补助",
                    path: "/pages/office/villageSubsidy",
                    isLogin: true,
                    role_require: [],
                },
                {
                    icon: "/static/common/office-icon4.png",
                    title: "车辆派遣",
                    path: "/pages/office/vehicleDispatch",
                    isLogin: true,
                    role_require: ["驻村办干事"],
                    // role_require: [],
                },
            ],
        };
    },
    onShow() {
        this.handleGetMyCenter();
    },
    methods: {
        // 获取个人中心数据
        async handleGetMyCenter() {
            try {
                const res = await this.$api.getMyCenter();
                if (res.code === 200) {
                    this.userInfo = res.data;
                    this.role_view = res.data.role_view.map((item) => {
                        if (item.role) {
                            return {
                                role_name: item.role.role_name,
                                role_extend: item.role.role_extend,
                            };
                        }
                    });
                }
            } catch (error) {
                console.log(error);
                this.$fn.showToast("获取个人中心数据失败");
            }
        },
        handleActionClick(item) {
            // 如果item.role_require为空数组，则不需要权限，直接跳转
            if (!item.role_require || item.role_require.length === 0) {
                this.$fn.jumpPage(item.path, item.isLogin);
                return;
            }
            const userRoles = this.role_view.map((role) => role.role_name);
            const hasPermission = userRoles.some((userRole) => item.role_require.includes(userRole));
            if (hasPermission) {
                this.$fn.jumpPage(item.path, item.isLogin);
            } else {
                this.$fn.showToast("您没有权限访问该页面");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fbfbfb;
    min-height: 100vh;

    .bg {
        .bg-img {
            width: 100%;
            height: 600rpx;
        }
    }

    .user-info {
        margin: -580rpx 24rpx 0 24rpx;
        position: relative;
        overflow: hidden;
        height: 192rpx;
        border-radius: 16rpx;
        box-shadow: 0rpx 12rpx 12rpx -4rpx rgba(224, 224, 224, 0.28);

        image {
            width: 100%;
            height: 192rpx;
        }

        .user-info-item {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 32rpx;

            .user-avatar {
                position: relative;

                .user-status {
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    background: #fd0100;
                    border-radius: 16rpx 0rpx 16rpx 0rpx;
                    padding: 6rpx 12rpx;
                }
            }
        }
    }

    .actions {
        padding: 24rpx;
        position: relative;
        z-index: 999;

        .actions-scroll {
            height: calc(100vh - 430rpx);

            .actions-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 22rpx;
            }
        }

        .action-item {
            width: 100%;
            height: auto;
            background: #fff;
            border-radius: 16rpx;
            padding: 48rpx 0;
            box-shadow: 0rpx 6rpx 21rpx 0rpx rgba(0, 0, 0, 0.05);

            image {
                width: 106rpx;
                height: 106rpx;
            }
        }
    }
}
</style>
