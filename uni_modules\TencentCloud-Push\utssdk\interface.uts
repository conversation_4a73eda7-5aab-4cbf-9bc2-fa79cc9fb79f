interface Push {
    setRegistrationID(registrationID: string,  onSuccess: () => void): void,
    registerPush(SDKAppID: number, appKey: string, onSuccess: (data: string) => void, onError?: (errCode: number, errMsg: string) => void): void,
    getRegistrationID(onSuccess: (registrationID: string) => void): void,
    unRegisterPush(onSuccess: () => void, onError?: (errCode: number, errMsg: string) => void): void,
    getNotificationExtInfo(onSuccess: (extInfo: string) => void): void
    addPushListener(eventName: string, listener: (res: any) => void): void
    removePushListener(eventName: string, listener?: (res: any) => void): void
    disablePostNotificationInForeground(disable: boolean): void
    createNotificationChannel(options: any, onSuccess: (data: string) => void): void
}
