<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="外出报备" :isBack="true" color="#000"> </c-navBar>
        </section>
        <section class="content">
            <u--form labelPosition="left" :model="form" :rules="rules" ref="uForm" labelWidth="220rpx" :labelStyle="{ color: '#1a1a1a', fontWeight: 'bold' }" errorType="toast">
                <view class="form">
                    <u-form-item label="外出地址" prop="address" borderBottom @click="handleChoosePicker('address')">
                        <view class="flex align-center justify-end gap-12" style="width: 100%">
                            <text v-if="form.address">{{ form.address }}</text>
                            <text v-else style="color: #c0c4cc">请选择出行目的地</text>
                            <view slot="right" style="margin-top: 2rpx">
                                <u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
                            </view>
                        </view>
                    </u-form-item>
                    <u-form-item label="开始时间" prop="start_date" borderBottom @click="handleChooseDate('start_date')">
                        <view class="flex align-center justify-end gap-12" style="width: 100%">
                            <text v-if="form.start_date">{{ $u.timeFormat(form.start_date, 'yyyy-mm-dd') }}</text>
                            <text v-else style="color: #c0c4cc">请选择开始时间</text>
                            <view slot="right" style="margin-top: 2rpx">
                                <u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
                            </view>
                        </view>
                    </u-form-item>
                    <u-form-item label="结束时间" prop="end_date" borderBottom @click="handleChooseDate('end_date')">
                        <view class="flex align-center justify-end gap-12" style="width: 100%">
                            <text v-if="form.end_date">{{ $u.timeFormat(form.end_date, 'yyyy-mm-dd') }}</text>
                            <text v-else style="color: #c0c4cc">请选择结束时间</text>
                            <view slot="right" style="margin-top: 2rpx">
                                <u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
                            </view>
                        </view>
                    </u-form-item>
                    <u-form-item label="离开时间" prop="go_time" borderBottom @click="handleChooseDate('go_time')">
                        <view class="flex align-center justify-end gap-12" style="width: 100%">
                            <text v-if="form.go_time">{{ $u.timeFormat(form.go_time, 'yyyy-mm-dd hh:MM') }}</text>
                            <text v-else style="color: #c0c4cc">请选择离开时间</text>
                            <view slot="right" style="margin-top: 2rpx">
                                <u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
                            </view>
                        </view>
                    </u-form-item>
                    <u-form-item label="外出事由" prop="reason" labelPosition="top">
                        <view class="textarea-box">
                            <u--textarea v-model="form.reason" placeholderStyle="color: #999" border="none" height="300rpx" placeholder="请输入报备事由"></u--textarea>
                        </view>
                    </u-form-item>
                    <u-form-item label="目的地" prop="destination" labelPosition="top">
                        <view class="textarea-box">
                            <u--textarea v-model="form.destination" placeholderStyle="color: #999" border="none" height="300rpx" placeholder="请输入目的地"></u--textarea>
                        </view>
                    </u-form-item>
                    <!-- <u-form-item label="交通方式" prop="transportation" borderBottom>
                        <u-input v-model="form.transportation" inputAlign="right" border="none" placeholder="请输入交通方式" />
                    </u-form-item> -->

                    <!-- <u-form-item label="图片上传" prop="images" labelPosition="top">
                        <view class="upload-box">
                            <c-upLoadImgs :maxCount="3" @update:file="handleUpdateFile"></c-upLoadImgs>
                        </view>
                    </u-form-item> -->
                </view>
            </u--form>
        </section>
        <section class="process">
            <view v-for="(chunk, chunkIndex) in approverChunks" :key="chunkIndex">
                <view class="process-list flex align-center justify-between">
                    <view class="process-item flex flex-col align-center gap-10" v-for="(item, index) in chunk" :key="item.id">
                        <view class="avatar">
                            <image class="avatar-img" :src="item.avatar || '/static/mine/avatar.png'" mode="scaleToFill" />
                            <view class="avatar-text mf-font-20" style="color: #fff">{{ item.role_name }}</view>
                        </view>
                        <text class="mf-font-28 mf-weight-bold u-line-1" style="color: #1a1a1a; text-align: center; width: 128rpx">
                            {{ item.nickname }}
                        </text>
                    </view>
                </view>
                <view class="steps">
                    <view class="flex align-center justify-between">
                        <view class="step-item flex justify-center" v-for="(item, index) in chunk" :key="item.id">
                            <view class="step-item-text flex flex-center mf-font-20">
                                {{ chunkIndex * 3 + index + 1 }}
                            </view>
                        </view>
                    </view>
                    <view class="step-line"></view>
                </view>
            </view>
        </section>
        <section class="footer">
            <view class="btn flex flex-center mf-font-36 mf-weight-bold" style="color: #fff" @click="handleSubmit">提交</view>
        </section>

        <u-datetime-picker :show="showDate" :value="currentDate" close-on-click-overlay @confirm="handleConfirmDate" @close="showDate = false" @cancel="showDate = false" :mode="dateType === 'go_time' ? 'datetime' : 'date'"></u-datetime-picker>
        <u-picker :show="showPicker" :columns="[address_list]" keyName="address" @confirm="handleConfirmPicker" @close="showPicker = false" @cancel="showPicker = false" close-on-click-overlay></u-picker>
    </view>
</template>

<script>
export default {
    data() {
        return {
            form: {
                is_out_county: 1, // 是否出市城
                lat: "", // 纬度
                lng: "", // 经度
                address: "", // 外出地址
                start_date: "", // 开始时间
                end_date: "", // 结束时间
                reason: "", // 报备事由
                destination: "", // 目的地
                // go_time: "", // 出行时间
            },
            approve_list: [], // 审批流程
            address_list: [], // 目的地列表
            rules: {
                lat: [{ required: true, type: "number", message: "位置信息获取失败" }],
                lng: [{ required: true, type: "number", message: "位置信息获取失败" }],
                address: [{ required: true, type: "string", message: "请选择外出地址" }],
                start_date: [{ required: true, type: "number", message: "请选择开始时间" }],
                end_date: [{ required: true, type: "number", message: "请选择结束时间" }],
                destination: [{ required: false, type: "string", message: "请输入出行目的地" }],
                reason: [{ required: true, type: "string", message: "请输入报备事由" }],
            },
            showDate: false, // 是否显示日期选择器
            currentDate: new Date().getTime(), // 当前日期
            showPicker: false, // 是否显示选择器
            columns: [], // 选择器数据
            dateType: "", // 日期类型（开始时间/结束时间）
        };
    },
    computed: {
        approverChunks() {
            // 按approve_node排序
            const sortedList = [...this.approve_list].sort((a, b) => parseFloat(a.approve_node) - parseFloat(b.approve_node));

            // 每3个一组进行分割
            const chunks = [];
            for (let i = 0; i < sortedList.length; i += 3) {
                chunks.push(sortedList.slice(i, i + 3));
            }
            return chunks;
        },
    },
    onReady() {
        this.$refs.uForm.setRules(this.rules);
    },
    onLoad() {
        this.handleGetOutReportData();
        this.handleGetLocation();
    },
    methods: {
        // 选择器数据处理
        handleChoosePicker(type) {
            this.columns = [];
            this.showPicker = true;
            this.currentPickerType = type;
        },
        // 选择日期
        handleChooseDate(type) {
            this.showDate = true;
            this.dateType = type; // 设置当前操作的日期类型（开始时间/结束时间）
        },
        // 确认日期选择
        handleConfirmDate(value) {
            this.form[this.dateType] = new Date(value.value).getTime();
            this.showDate = false;
        },
        // 确认选择器选择
        handleConfirmPicker(value) {
            this.form.address = value.value[0].address; // 设置选中的值
            this.form.is_out_county = value.value[0].is_out;
            this.showPicker = false;
        },
        // 表单提交
        handleSubmit() {
            console.log(this.form);
            if (this.form.start_date > this.form.end_date) {
                this.$fn.showToast("开始时间不能大于结束时间");
                return;
            }
            this.$refs.uForm.validate().then((res) => {
                if (res) {
                    this.handleOutReport();
                }
            });
        },
        // 更新上传的图片
        handleUpdateFile(files) {
            this.form.images = files; // 更新图片数组
        },
        // 获取当前定位
        async handleGetLocation() {
            try {
                const res = await this.$fn.getLocation();
                this.form.lat = res.latitude;
                this.form.lng = res.longitude;
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取位置信息失败");
            }
        },
        // 获取外出报备数据
        async handleGetOutReportData() {
            try {
                const res = await this.$api.getOutReportData({ is_out_county: 1 });
                if (res.code === 200) {
                    // 按approve_step从小到大排序
                    this.approve_list = res.data.approve_list;
                    this.address_list = res.data.address_list;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取外出报备数据失败");
            }
        },
        // 外出报备
        async handleOutReport() {
            try {
                const res = await this.$api.outReport({
                    ...this.form,
                    start_date: this.$u.timeFormat(this.form.start_date, "yyyy-mm-dd"),
                    end_date: this.$u.timeFormat(this.form.end_date, "yyyy-mm-dd"),
                    go_time: this.$u.timeFormat(this.form.go_time, "yyyy-mm-dd hh:MM"),
                });
                if (res.code === 200) {
                    this.$fn.showToast("外出报备成功");
                    setTimeout(() => {
                        this.$fn.jumpBack();
                    }, 1000);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("外出报备失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        .form {
            padding: 24rpx 24rpx 0 24rpx;
            background-color: #fff;
            border-radius: 24rpx;

            .textarea-box {
                margin-top: 24rpx;
                width: 100%;

                ::v-deep .u-textarea {
                    background-color: #f7f8fa;
                }

                ::v-deep .u-textarea__count {
                    background-color: #f7f8fa !important;
                }
            }

            .upload-box {
                margin-top: 24rpx;
            }
        }
    }

    .process {
        padding: 16rpx 24rpx;
        padding-bottom: 220rpx;

        position: relative;

        .process-list {
            margin-top: 20rpx;

            .process-item {
                .avatar {
                    width: 128rpx;
                    height: 128rpx;
                    border-radius: 16rpx;
                    position: relative;

                    .avatar-img {
                        width: 128rpx;
                        height: 128rpx;
                    }

                    .avatar-text {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        text-align: center;
                        background: #ebb84c;
                        border-radius: 0 0 16rpx 16rpx;
                    }
                }
            }
        }

        .steps {
            margin-top: 20rpx;
            z-index: 10;
            position: relative;

            .step-item {
                width: 128rpx;
                margin-right: 3rpx; // 微调位置

                .step-item-text {
                    width: 35rpx;
                    height: 35rpx;
                    border-radius: 50%;
                    background: #fd0100;
                    text-align: center;
                    color: #fff;
                }
            }

            .step-line {
                position: absolute;
                bottom: 17rpx;
                left: 0;
                right: 0;
                width: 100%;
                height: 2rpx;
                z-index: -1;
                border-bottom: 2rpx dashed #00000060;
            }
        }

        .process-btn {
            margin-top: 128rpx;

            .btn {
                width: 702rpx;
                height: 96rpx;
                background: #fd0100;
                border-radius: 12rpx;
            }
        }
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: #fbfbfb;
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
        z-index: 999;

        .btn {
            width: 100%;
            height: 100%;
            background: #fd0100;
            border-radius: 12rpx;
        }
    }
}
</style>
