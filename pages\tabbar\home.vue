<template>
    <view class="view">
        <section class="home-bg">
            <image src="/static/common/home-bg.png" mode="scaleToFill" />
        </section>
        <section class="navbar">
            <c-navBar custom isTran isPerch :isBack="false" color="#fff">
                <view class="header flex justify-between align-center">
                    <view class="mf-font-32 mf-weight-bold">驻村管理</view>
                    <view class="flex align-center gap-32">
                        <view class="icon-box flex align-center" @click="handleStatistics">
                            <u-image src="/static/common/data-icon.png" width="64rpx" height="64rpx" mode="scaleToFill" />
                            <view class="icon-text">数据统计</view>
                        </view>
                        <view class="icon-box flex align-center" @click="handleSettings">
                            <u-image src="/static/common/setting-icon.png" width="64rpx" height="64rpx" mode="scaleToFill" />
                            <view class="icon-text">设置</view>
                        </view>
                    </view>
                </view>
            </c-navBar>
        </section>
        <section class="user-info">
            <image src="/static/common/user-card.png" mode="scaleToFill" />
            <view class="user-info-item">
                <view class="flex align-center gap-24" style="padding: 32rpx 32rpx 0 32rpx">
                    <view class="user-avatar">
                        <u-image
                            :src="userInfo.avatar || '/static/mine/avatar.png'"
                            width="128rpx"
                            height="128rpx"
                            radius="16rpx"
                            mode="aspectFill"></u-image>
                        <view class="user-status mf-font-20" style="color: #fff">
                            {{ userInfo.online_status ? userInfo.online_status.name : "获取失败" }}
                        </view>
                    </view>
                    <view class="flex flex-col gap-30">
                        <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ userInfo.nickname }}</view>
                        <view class="mf-font-28" style="color: #ebb84c">{{ userInfo.role ? userInfo.role.role_name : "获取失败" }}</view>
                    </view>
                </view>
                <view class="flex align-center" style="margin-top: 48rpx">
                    <view class="flex-1 flex flex-col gap-18 align-center">
                        <text class="mf-font-40 mf-weight-bold" style="color: #1a1a1a">{{ clockCenterData.today_is_clock }}</text>
                        <text class="mf-font-24" style="color: #999">本月补领</text>
                    </view>
                    <view class="line"></view>
                    <view class="flex-1 flex flex-col gap-18 align-center">
                        <text class="mf-font-40 mf-weight-bold" style="color: #1a1a1a">{{ clockCenterData.leave_days }}</text>
                        <text class="mf-font-24" style="color: #999">本月请假</text>
                    </view>
                    <view class="line"></view>
                    <view class="flex-1 flex flex-col gap-18 align-center">
                        <text class="mf-font-40 mf-weight-bold" style="color: #1a1a1a">{{ clockCenterData.absenteeism_days }}</text>
                        <text class="mf-font-24" style="color: #999">本月旷工</text>
                    </view>
                    <view class="line"></view>
                    <view class="flex-1 flex flex-col gap-18 align-center">
                        <text class="mf-font-40 mf-weight-bold" style="color: #1a1a1a">{{ clockCenterData.record_days }}</text>
                        <text class="mf-font-24" style="color: #999">本月报备</text>
                    </view>
                </view>
            </view>
        </section>

        <section class="check-in-section">
            <view
                class="clock-button-outer"
                @click="handleClockIn"
                :style="{
                    backgroundColor:
                        checkInType === 0
                            ? '#FBFBFB'
                            : checkInType === 1
                            ? '#FFF5F0'
                            : checkInType === 2
                            ? '#FFEFEF'
                            : checkInType === 3
                            ? '#FFF5F0'
                            : checkInType === 4
                            ? '#FFEFEF'
                            : checkInType === 5
                            ? '#FBFBFB'
                            : '',
                }">
                <view
                    class="clock-button-middle"
                    :style="{
                        backgroundColor:
                            checkInType === 0
                                ? '#F2F2F2'
                                : checkInType === 1
                                ? '#FFDCCD'
                                : checkInType === 2
                                ? '#FFC9C9'
                                : checkInType === 3
                                ? '#FFDCCD'
                                : checkInType === 4
                                ? '#FFC9C9'
                                : checkInType === 5
                                ? '#F2F2F2'
                                : '',
                    }">
                    <view
                        class="clock-button-inner flex flex-col flex-center"
                        :style="{
                            backgroundColor:
                                checkInType === 0
                                    ? '#CCCCCC'
                                    : checkInType === 1
                                    ? '#FD7840'
                                    : checkInType === 2
                                    ? '#FD0100'
                                    : checkInType === 3
                                    ? '#FD7840'
                                    : checkInType === 4
                                    ? '#FD0100'
                                    : checkInType === 5
                                    ? '#CCCCCC'
                                    : '',
                        }">
                        <view class="location-icon">
                            <image
                                src="/static/common/check-in.png"
                                v-if="checkInType === 1 || checkInType === 3"
                                mode="aspectFill"></image>
                            <image src="/static/common/check-in2.png" v-if="checkInType === 2" mode="aspectFill"> </image>
                            <image
                                src="/static/common/check-in3.png"
                                v-if="checkInType === 0 || checkInType === 4"
                                mode="aspectFill"></image>
                            <image src="/static/common/check-in3.png" v-if="checkInType === 5" mode="aspectFill"> </image>
                        </view>
                        <text class="mf-font-32 mf-weight-bold check-text" v-if="checkInType === 0">不在领补助时间</text>
                        <text class="mf-font-32 mf-weight-bold check-text" v-if="checkInType === 5">暂无领补助权限</text>
                        <text class="mf-font-32 mf-weight-bold check-text" v-if="checkInType === 1 || checkInType === 2">今日已领补助</text>
                        <text class="mf-font-32 mf-weight-bold check-text" v-if="checkInType === 3">外勤领补助</text>
                        <text class="mf-font-32 mf-weight-bold check-text" v-if="checkInType === 4">领补助</text>
                        <text class="mf-font-28 check-time">{{ currentTime }}</text>
                        <!-- <text class="mf-font-20 end_time" v-if="checkInType === 2 && clockCenterData.village">
                            请在{{ clockCenterData.village.clock_end_time }}后领补助
                        </text> -->
                    </view>
                </view>
            </view>
        </section>

        <section class="location-info">
            <view class="clock-time" v-if="clockCenterData.village">
                <text class="mf-font-28">
                    领补助时间：{{ clockCenterData.village.clock_start_time }} - {{ clockCenterData.village.clock_end_time }}
                </text>
            </view>
            <view class="location-status flex flex-center" v-if="addressInfo.address">
                <image src="/static/common/select.png" v-if="checkInType !== 5" mode="aspectFill" class="status-icon"> </image>
                <text class="mf-font-24" v-if="checkInType === 2 || checkInType === 4">你已进入考勤范围：{{ addressInfo.address }}</text>
                <text class="mf-font-24" v-if="checkInType === 0 || checkInType === 1 || checkInType === 3">
                    你当前不在考勤范围：{{ addressInfo.address }}
                </text>
            </view>
            <view class="location-status flex flex-center" v-else>
                <u-icon name="warning-fill" color="#fd0100" size="18"></u-icon>
                <text class="mf-font-24">定位获取失败</text>
            </view>
            <view class="relocate flex flex-center" @click="handleGetLocation">
                <!-- <image src="/static/common/relocate.svg" mode="aspectFill" class="relocate-icon"></image> -->
                <text class="mf-font-28">重新定位？</text>
            </view>
        </section>

        <section class="punch-button-container flex flex-center">
            <view class="punch-button flex flex-center" @click="handleSupplementClock">
                <text class="mf-font-32 mf-weight-bold">补领</text>
            </view>
        </section>
        <!-- 外勤领补助弹窗 -->
        <u-popup :show="fieldCheckIn" @close="fieldCheckIn = false" :safeAreaInsetBottom="false" mode="center" round="16rpx">
            <view class="field-check-in-popup">
                <view class="content">
                    <view class="mf-font-32 mf-weight-bold" style="text-align: center; color: #1a1a1a">外勤报备</view>
                    <view class="flex align-center mf-font-28 mf-weight-bold" style="margin-top: 28rpx">
                        <text style="color: #666">领补助时间：</text>
                        <text style="color: #fd0100">{{ currentCheckinTime }}</text>
                    </view>
                    <view class="flex align-center gap-6 mf-font-28" style="margin-top: 28rpx">
                        <u-image src="/static/common/location.png" width="36rpx" height="36rpx" mode="aspectFill"></u-image>
                        <text style="color: #666">{{ addressInfo.address }}</text>
                    </view>
                    <view style="margin-top: 28rpx">
                        <u--textarea
                            v-model="fieldCheckInRemark"
                            class="field-check-in-textarea"
                            placeholderStyle="color: #999"
                            count
                            border="none"
                            height="300rpx"
                            maxlength="80"
                            placeholder="请备注外勤报备原因"></u--textarea>
                    </view>
                </view>
                <view class="footer flex align-center justify-between">
                    <view class="footer-button flex-1 flex flex-center" @click="fieldCheckIn = false">
                        <text class="mf-font-28" style="color: #fd0100">不打了</text>
                    </view>
                    <view class="line"></view>
                    <view class="footer-button flex-1 flex flex-center" @click="checkInIncident">
                        <text class="mf-font-28" style="color: #fd0100">提交</text>
                    </view>
                </view>
            </view>
        </u-popup>
        <!-- 领补助成功弹窗 -->
        <u-popup :show="checkinSuccess" @close="checkinSuccess = false" :safeAreaInsetBottom="false" mode="center" round="16rpx">
            <view class="success-popup">
                <view class="content">
                    <image src="/static/common/checkin-bg.png" mode="scaleToFill" class="checkin-bg" />
                    <view class="title mf-font-32 mf-weight-bold" style="text-align: center; color: #1a1a1a">领补助成功 </view>
                    <text class="time flex justify-center mf-font-48 mf-weight-bold" style="color: #fd0100; z-index: 3; text-align: center">
                        {{ currentCheckinTime }}
                    </text>
                    <view class="checkin-info flex flex-center gap-6 mf-font-28">
                        <u-image src="/static/common/location.png" width="36rpx" height="36rpx" mode="aspectFill"></u-image>
                        <text style="color: #666">{{ addressInfo.address }}</text>
                    </view>
                </view>
                <view class="footer flex align-center justify-between">
                    <view class="footer-button flex-1 flex flex-center" @click="handleCheckinSuccess">
                        <text class="mf-font-28" style="color: #fd0100">我知道了</text>
                    </view>
                </view>
            </view>
        </u-popup>
    </view>
</template>
<script>
import { startFaceVerify } from "@/common/js/cloudIai.js";
export default {
    data() {
        return {
            fieldCheckIn: false,
            currentTime: "",
            checkInType: 0, // 领补助状态 0:不在领补助时间 1:已领补助不在范围 2:已领补助在范围 3:未领补助不在范围 4:未领补助在范围
            fieldCheckInRemark: "",
            checkinSuccess: false,
            currentCheckinTime: "",
            userInfo: {}, // 用户信息
            clockCenterData: {}, // 领补助中心数据
            timer: null, // 定时器
            addressInfo: {}, // 地名信息
            currentLocation: {}, // 当前定位
            role_view: [], // 角色权限
            isFace: "", //人脸认证结果 ok:成功 error:失败
        };
    },
    onShow() {
        this.handleGetMyCenter();
        setTimeout(() => {
            this.handleGetClockCenter();
        }, 1000);
    },
    onLoad() {
        this.updateTime();
        // 设置定时器，每秒更新一次时间
        this.timer = setInterval(() => {
            this.updateTime();
        }, 1000);
    },
    onUnload() {
        // 页面卸载时清除定时器
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    },
    methods: {
        // 领补助成功
        handleCheckinSuccess() {
            this.checkinSuccess = false;
            this.handleGetClockCenter();
            this.handleGetLocation();
        },
        // 获取领补助中心数据
        async handleGetClockCenter() {
            try {
                const res = await this.$api.clockCenter();
                if (res.code === 200) {
                    this.clockCenterData = res.data;
                    this.handleGetLocation();
                }
            } catch (error) {
                console.log(error);
                this.$fn.showToast("获取领补助中心数据失败");
            }
        },

        // 获取当前定位
        async handleGetLocation() {
            uni.showLoading({
                title: "获取定位中...",
            });
            try {
                const res = await this.$fn.getLocation();

                if (!res || !res.latitude || !res.longitude) {
                    this.$fn.showToast("获取定位失败");
                    return;
                }

                this.currentLocation = {
                    lat: res.latitude,
                    lng: res.longitude,
                };

                // 获取地名
                await this.handleGetLocationName(`${res.latitude},${res.longitude}`);
                console.log("领补助中心：", this.clockCenterData);

                // 获取村委信息
                const villageInfo = this.clockCenterData?.village;
                if (!villageInfo || !villageInfo.clock_lat || !villageInfo.clock_lng) {
                    this.checkInType = 5;
                    this.$fn.showToast("获取村委信息失败");
                    return;
                }

                // 计算距离
                const distance = await this.$fn.calculateDistance(
                    { lat: res.latitude, lng: res.longitude },
                    { lat: villageInfo.clock_lat, lng: villageInfo.clock_lng }
                );

                console.log("距离(km):", distance / 1000);
                // 判断是否在领补助范围
                const isInRange = distance / 1000 <= villageInfo.clock_range;
                // 判断是否已领补助
                const isCheckedIn = this.clockCenterData.today_is_clock === 1;

                if (isCheckedIn) {
                    // 今日已领补助
                    this.checkInType = isInRange ? 2 : 1;
                } else {
                    // 今日未领补助 判断是否在领补助时间段内
                    const isWithInTime = this.$fn.isWithinTimeRange(villageInfo.clock_start_time, villageInfo.clock_end_time);
                    if (isWithInTime) {
                        this.checkInType = isInRange ? 4 : 3;
                    } else {
                        this.checkInType = 0;
                    }
                }
                console.log("领补助状态：", this.checkInType);
            } catch (error) {
                console.error("获取定位失败:", error);
                this.$fn.showToast("获取定位失败");
            } finally {
                uni.hideLoading();
                if (uni.getStorageSync("faceRecognitionSuccess")) {
                    this.isFace = uni.getStorageSync("faceRecognitionSuccess");
                    if (this.isFace == "error") {
                        this.fieldCheckIn = false;
                        this.$fn.showToast("人脸认证失败,请重试");
                    } else if (this.isFace == "ok") {
                        this.submitCheckIn();
                    } else {
                        this.fieldCheckIn = false;
                        this.$fn.showToast("获取人脸认证结果失败");
                    }
                    uni.removeStorageSync("faceRecognitionSuccess");
                }
            }
        },

        // 获取当前地名
        async handleGetLocationName(location) {
            try {
                const res = await this.$fn.getLocationName(
                    "U2KBZ-NDELN-R2CF5-SDW3U-5QPI7-SXFI4",
                    location,
                    "4wXlpKbn0V8GQYCDaTLdv07bowPYt5x5"
                );
                if (res.statusCode === 200) {
                    this.addressInfo = res.data.result;
                    // console.log("逆解析地址：", this.addressInfo);
                } else {
                    this.$fn.showToast("获取地名失败");
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取地名失败");
            }
        },

        // 获取个人中心数据
        async handleGetMyCenter() {
            try {
                const res = await this.$api.getMyCenter();
                if (res.code === 200) {
                    this.userInfo = res.data;
                    this.role_view = res.data.role_view.map((item) => {
                        if (item.role) {
                            return {
                                role_name: item.role.role_name,
                                role_extend: item.role.role_extend,
                            };
                        }
                    });
                    uni.setStorageSync("userInfo", this.userInfo);
                    // 新增：检查用户登录状态，如果已登录，则调用初始化函数
                    // #ifdef APP-PLUS
                    if (this.userInfo && this.userInfo.id) {
                        console.log("App.vue: 检测到用户已登录，开始初始化IM及音视频服务...");
                    } else {
                        console.log("App.vue: 用户未登录，跳过初始化。");
                    }
                    // #endif
                }
            } catch (error) {
                console.log(error);
                this.$fn.showToast("获取个人中心数据失败");
            }
        },
        // 更新时间
        updateTime() {
            const now = new Date();
            const hours = now.getHours().toString().padStart(2, "0");
            const minutes = now.getMinutes().toString().padStart(2, "0");
            const seconds = now.getSeconds().toString().padStart(2, "0");
            this.currentTime = `${hours}:${minutes}:${seconds}`;
        },
        // 领补助事件
        async checkInIncident() {
            try {
                if (this.checkInType === 3 && !this.fieldCheckInRemark) {
                    this.$fn.showToast("请填写外勤领补助原因");
                    return;
                }

                if (this.userInfo.face_detect === 1) {
                    // --- 开始活体人脸核身 ---
                    // 请在腾讯云控制台配置核身规则，并在此处填入RuleId
                    const ruleId = "1";
                    this.$fn.showToast("正在启动人脸核身...");
                    const verifyRes = await startFaceVerify(ruleId);
                    console.log("人脸结果：", verifyRes);
                } else {
                    this.submitCheckIn();
                }
            } catch (error) {
                console.log(error);
                // this.$fn.showToast("领补助失败");
            }
        },
        async submitCheckIn() {
            this.$fn.showToast("正在领补助...");
            const data = {
                lat: this.currentLocation.lat,
                lng: this.currentLocation.lng,
                province: this.addressInfo.ad_info.province,
                city: this.addressInfo.ad_info.city,
                poi_address: this.addressInfo.address,
                record: this.fieldCheckInRemark,
            };
            let res;
            if (this.checkInType === 3) {
                res = await this.$api.outClock(data); // 外勤领补助
            } else {
                res = await this.$api.clock(data); // 领补助
            }
            if (res.code === 200) {
                this.fieldCheckIn = false; // 外勤领补助弹窗
                this.checkinSuccess = true; // 领补助成功弹窗
                this.currentCheckinTime = this.currentTime;
            }
            this.handleGetClockCenter();
        },
        // 领补助
        handleClockIn() {
            console.log(
                "当前领补助状态：",
                this.checkInType === 0
                    ? "不在领补助时间"
                    : this.checkInType === 1
                    ? "已领补助不在范围"
                    : this.checkInType === 2
                    ? "已领补助在范围"
                    : this.checkInType === 3
                    ? "未领补助不在范围"
                    : this.checkInType === 4
                    ? "未领补助在范围"
                    : this.checkInType === 5
                    ? "暂无领补助权限"
                    : "未知状态"
            );
            if (!this.addressInfo.address) {
                this.$fn.showToast("定位获取失败, 请重新定位或检查权限");
                return;
            }
            if (this.clockCenterData.today_is_clock === 1) {
                uni.showModal({
                    title: "提示",
                    content: "今日已领补助，请勿重复领补助",
                    showCancel: false,
                    confirmText: "我晓得了",
                    success: ({ confirm, cancel }) => {},
                    fail: (err) => {
                        console.log(err);
                    },
                });
                return;
            }
            switch (this.checkInType) {
                case 0: // 不在领补助时间
                    this.$fn.showToast("不在领补助时间内，请在规定时间内领补助或使用补卡功能");
                    break;
                case 1: // 已领补助不在范围
                case 2: // 已领补助在范围
                    break;
                case 3: // 未领补助不在范围(外勤领补助)
                    this.fieldCheckIn = true;
                    this.currentCheckinTime = this.currentTime;
                    break;
                case 4: // 未领补助在范围(正常领补助)
                    this.checkInIncident();
                    break;
                case 5: // 暂无领补助权限
                    this.$fn.showToast("暂无领补助权限");
                    break;
            }
        },
        // 补领补助
        handleSupplementClock() {
            const data = {
                lat: this.currentLocation.lat,
                lng: this.currentLocation.lng,
                province: this.addressInfo.ad_info.province,
                city: this.addressInfo.ad_info.city,
                poi_address: this.addressInfo.address,
                date: this.currentTime,
                record: this.fieldCheckInRemark,
            };
            this.$fn.jumpPage(`/pages/checkIn/supplementClock?data=${JSON.stringify(data)}`, true);
        },

        // 设置
        handleSettings() {
            this.$fn.jumpPage("/pages/checkIn/settings", true);
        },
        // 统计
        handleStatistics() {
            const data = {
                lat: this.currentLocation.lat,
                lng: this.currentLocation.lng,
                province: this.addressInfo.ad_info.province,
                city: this.addressInfo.ad_info.city,
                poi_address: this.addressInfo.address,
                date: this.currentTime,
                record: this.fieldCheckInRemark,
            };
            this.$fn.jumpPage(`/pages/checkIn/statistics?data=${JSON.stringify(data)}`, true);
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    position: relative;
    min-height: 100vh;

    .home-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 600rpx;
        z-index: -999;

        image {
            width: 100%;
            height: 100%;
        }
    }

    .navbar {
        .header {
            padding: 30rpx 24rpx;
            color: #fff;
        }
    }

    .user-info {
        margin: 30rpx 24rpx 0;
        position: relative;

        image {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 702rpx;
            height: 340rpx;
        }

        .user-info-item {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 702rpx;
            height: 340rpx;
            border-radius: 16rpx;

            .user-avatar {
                position: relative;

                .user-status {
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    background: #fd0100;
                    border-radius: 16rpx 0rpx 16rpx 0rpx;
                    padding: 6rpx 12rpx;
                }
            }

            .line {
                width: 2rpx;
                height: 48rpx;
                background: #e6e6e6;
            }
        }
    }

    .check-in-section {
        display: flex;
        justify-content: center;
        margin-top: 460rpx;

        .clock-button-outer {
            width: 436rpx;
            height: 436rpx;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .clock-button-middle {
                width: 372rpx;
                height: 372rpx;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;

                .clock-button-inner {
                    width: 308rpx;
                    height: 308rpx;
                    border-radius: 50%;
                    color: #fff;

                    .location-icon {
                        margin-bottom: 10rpx;

                        image {
                            width: 98rpx;
                            height: 98rpx;
                        }
                    }

                    .check-text {
                        margin-bottom: 10rpx;
                    }

                    .check-time {
                        color: #fff;
                    }
                }
            }
        }
    }

    .location-info {
        margin-top: 40rpx;

        .clock-time {
            text-align: center;
            margin-bottom: 20rpx;
        }

        .location-status {
            .status-icon {
                width: 32rpx;
                height: 32rpx;
                margin-right: 10rpx;
            }
        }

        .relocate {
            margin-top: 20rpx;
            color: #fd0100;
            margin-left: 16rpx;

            .relocate-icon {
                width: 30rpx;
                height: 30rpx;
                margin-right: 8rpx;
            }
        }
    }

    .punch-button-container {
        margin-top: 40rpx;

        .punch-button {
            padding: 17rpx 50rpx;
            border: 2rpx solid #fd0100;
            border-radius: 48rpx;
            color: #fd0100;
        }
    }

    .field-check-in-popup {
        width: 624rpx;
        height: 694rpx;
        background: #ffffff;
        border-radius: 16rpx;
        position: relative;

        .content {
            padding: 32rpx 24rpx;

            ::v-deep .u-textarea {
                background-color: #fbf6f6;
            }

            ::v-deep .u-textarea__count {
                background-color: #fbf6f6 !important;
            }
        }

        .footer {
            height: 80rpx;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;

            .footer-button {
                height: 80rpx;
                border-top: 2rpx solid #e6e6e6;
            }

            .line {
                width: 2rpx;
                height: 80rpx;
                background: #e6e6e6;
            }
        }
    }

    .success-popup {
        width: 624rpx;
        height: 694rpx;
        border-radius: 16rpx;
        position: relative;

        .content {
            .checkin-bg {
                width: 624rpx;
                height: 400rpx;
            }

            .title {
                position: absolute;
                top: 32rpx;
                left: 50%;
                transform: translateX(-50%);
                z-index: 2;
            }

            .time {
                text-align: center;
            }

            .checkin-info {
                padding: 32rpx 24rpx;
                text-align: center;
                z-index: 2;
            }
        }

        .footer {
            height: 80rpx;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;

            .footer-button {
                height: 80rpx;
                border-top: 2rpx solid #e6e6e6;
            }
        }
    }
}
</style>
