<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="请假申请" :isBack="true" showRight>
                <view slot="right">
                    <text class="mf-font-28 mf-weight-bold" style="color: #999999" @click="$fn.jumpPage('/pages/office/leaveRecord')">请假记录</text>
                </view>
            </c-navBar>
        </section>
        <section class="tabs-box">
            <view class="tabs mf-font-28 mf-weight-bold flex align-center justify-between">
                <view class="tab-item flex-1 flex flex-center" @click="handleTab('self')" :class="{ active: currentTab === 'self' }" style="border-radius: 12rpx 0rpx 0rpx 12rpx">
                    <text>本人请假</text>
                </view>
                <view class="tab-item flex-1 flex flex-center" @click="handleTab('other')" :class="{ active: currentTab === 'other' }" style="border-radius: 0rpx 12rpx 12rpx 0rpx">
                    <text>代他人请假</text>
                </view>
            </view>
        </section>
        <section class="content">
            <u--form labelPosition="left" :model="form" :rules="rules" ref="uForm" errorType="toast" labelWidth="220rpx" :labelStyle="{ color: '#1a1a1a', fontWeight: 'bold' }">
                <view class="form">
                    <u-form-item v-if="currentTab === 'other'" label="请假人员" prop="user_id" borderBottom rightIcon="arrow-right" @click="showPersonOnLeavePopup = true" required>
                        <view class="flex align-center justify-end gap-12" style="width: 100%">
                            <text v-if="form.user_id">{{ form.user_id.label }}</text>
                            <text v-else style="color: #c0c4cc">请选择请假人员</text>
                            <view slot="right" style="margin-top: 2rpx">
                                <u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
                            </view>
                        </view>
                    </u-form-item>
                    <u-form-item v-if="currentTab === 'other'" label="请假地址" prop="poi_address" borderBottom @click="handleChooseLocation" required>
                        <view class="flex align-center justify-end gap-12" style="width: 100%">
                            <text v-if="form.poi_address">{{ form.poi_address }}</text>
                            <text v-else style="color: #c0c4cc">请选择请假地址</text>
                            <view slot="right" style="margin-top: 2rpx">
                                <u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
                            </view>
                        </view>
                    </u-form-item>
                    <u-form-item label="请假类别" prop="leave_type" borderBottom @click="shwoLeaveTypePicker = true" required>
                        <view class="flex align-center justify-end gap-12" style="width: 100%">
                            <text v-if="form.leave_type && form.leave_type.label">{{ form.leave_type.label }}</text>
                            <text v-else style="color: #c0c4cc">请选择请假类别</text>
                            <view slot="right" style="margin-top: 2rpx">
                                <u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
                            </view>
                        </view>
                    </u-form-item>
                    <u-form-item label="开始时间" prop="start_date" borderBottom @click="handleChooseDate('start_date')" required>
                        <view class="flex align-center justify-end gap-12" style="width: 100%">
                            <text v-if="form.start_date">
                                {{ form.start_date ? $u.timeFormat(form.start_date, "yyyy-mm-dd") : "" }}
                            </text>
                            <text v-else style="color: #c0c4cc">请选择开始时间</text>
                            <view slot="right" style="margin-top: 2rpx">
                                <u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
                            </view>
                        </view>
                    </u-form-item>
                    <u-form-item label="结束时间" prop="end_date" borderBottom @click="handleChooseDate('end_date')" required>
                        <view class="flex align-center justify-end gap-12" style="width: 100%">
                            <text v-if="form.end_date">
                                {{ form.end_date ? $u.timeFormat(form.end_date, "yyyy-mm-dd") : "" }}
                            </text>
                            <text v-else style="color: #c0c4cc">请选择结束时间</text>
                            <view slot="right" style="margin-top: 2rpx">
                                <u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
                            </view>
                        </view>
                    </u-form-item>
                    <u-form-item label="离开时间" prop="go_time" borderBottom @click="handleChooseDate('go_time')" required>
                        <view class="flex align-center justify-end gap-12" style="width: 100%">
                            <text v-if="form.go_time">
                                {{ form.go_time ? $u.timeFormat(form.go_time, "yyyy-mm-dd hh:MM") : "" }}
                            </text>
                            <text v-else style="color: #c0c4cc">请选择离开时间</text>
                            <view slot="right" style="margin-top: 2rpx">
                                <u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
                            </view>
                        </view>
                    </u-form-item>
                    <u-form-item label="请假天数" prop="go_time" borderBottom @click="handleChooseDate('go_time')" required>
                        <view class="flex align-center justify-end gap-12" style="width: 100%">
                            <text v-if="form.go_time">
                            </text>
                            <text v-else style="color: #c0c4cc">请选择开始/结束时间</text>
                            <view slot="right" style="margin-top: 2rpx">
                                <u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
                            </view>
                        </view>
                    </u-form-item>
                    <u-form-item label="到达地点" prop="destination" borderBottom labelPosition="top" required>
                        <view class="textarea-box">
                            <u--textarea v-model="form.destination" placeholderStyle="color: #999" border="none" height="100rpx" maxlength="80" placeholder="请输入到达地点"></u--textarea>
                        </view>
                    </u-form-item>
                    <u-form-item label="请假事由" prop="record" borderBottom labelPosition="top" required>
                        <view class="textarea-box">
                            <u--textarea v-model="form.record" placeholderStyle="color: #999" border="none" height="300rpx" maxlength="80" placeholder="请输入请假事由"></u--textarea>
                        </view>
                    </u-form-item>
                    <u-form-item label="图片上传" prop="images" labelPosition="top">
                        <view class="upload-box">
                            <c-upLoadImgs :maxCount="3" @update:file="handleUpdateFile"></c-upLoadImgs>
                            <!-- <c-upload-cloud :maxCount="3" @update:file="handleUpdateFile"></c-upload-cloud> -->
                        </view>
                    </u-form-item>
                </view>
            </u--form>
        </section>
        <view class="line"></view>
        <section class="process">
            <view v-for="(chunk, chunkIndex) in approverChunks" :key="chunkIndex">
                <view class="process-list flex align-center justify-between">
                    <view class="process-item flex flex-col align-center gap-10" v-for="(item, index) in chunk" :key="item.id">
                        <view class="avatar">
                            <image class="avatar-img" :src="item.avatar || '/static/mine/avatar.png'" mode="scaleToFill" />
                            <view class="avatar-text mf-font-20" style="color: #fff">{{ item.role_name }}</view>
                        </view>
                        <text class="mf-font-28 mf-weight-bold u-line-1" style="color: #1a1a1a; width: 128rpx; text-align: center">
                            {{ item.nickname }}
                        </text>
                    </view>
                </view>
                <view class="steps">
                    <view class="flex align-center justify-between">
                        <view class="step-item flex justify-center" v-for="(item, index) in chunk" :key="item.id">
                            <view class="step-item-text flex flex-center mf-font-20">
                                {{ chunkIndex * 3 + index + 1 }}
                            </view>
                        </view>
                    </view>
                    <view class="step-line"></view>
                </view>
            </view>
        </section>
        <section class="footer">
            <view class="btn flex flex-center mf-font-36 mf-weight-bold" style="color: #fff" @click="handleSubmit">提交</view>
        </section>
        <!-- 请假类型 -->
        <u-picker :show="shwoLeaveTypePicker" :columns="[leave_types]" keyName="label" closeOnClickOverlay @cancel="shwoLeaveTypePicker = false" @close="shwoLeaveTypePicker = false" @confirm="confirmChooseLeaveType"></u-picker>
        <!-- 时间选择 -->
        <u-datetime-picker :show="showDateChoosePicker" :mode="dateMode" :minDate="minDate" closeOnClickOverlay @cancel="showDateChoosePicker = false" @close="showDateChoosePicker = false" @confirm="confirmChooseDate"></u-datetime-picker>
        <!-- 请假类型 -->
        <u-picker :show="showPersonOnLeavePopup" :columns="[personOnLeaveList]" keyName="label" closeOnClickOverlay @cancel="showPersonOnLeavePopup = false" @close="showPersonOnLeavePopup = false" @confirm="handleSelectPersonOnLeave"></u-picker>
    </view>
</template>

<script>
export default {
    data() {
        return {
            currentTab: "self",
            shwoLeaveTypePicker: false, // 请假类型
            showDateChoosePicker: false, // 时间选择
            showPersonOnLeavePopup: false, // 请假人选择弹窗
            dateMode: "datetime", // 选择器时间类型 date/datetime
            currentDateType: "", // 当前时间类型 start_date/end_date
            minDate: Number(new Date()),
            leaveData: {}, // 请假相关数据
            leave_types: [], // 请假类型
            approve_list: [], // 请假审核节点
            village_user_list: [], // 驻村人员列表(能代请假的用户列表)
            personOnLeaveList: [], // 用户列表
            keywords: "", // 搜索关键词
            form: {
                lat: "", // 纬度 必填
                lng: "", // 经度 必填
                province: "", // 省
                city: "", // 市
                poi_address: "", // 地址详情
                start_date: "", // 开始时间 必填
                end_date: "", // 结束时间 必填
                leave_type: {}, // 请假类型  1~9 必填
                record: "", // 请假原因
                images: "", // 图片
                destination: "", // 目的地
                go_time: "", // 离开时间 必填
                is_continue: 0, // 是否够连续,默认0
            },
            rules: {
                user_id: [{ required: true, message: "请选择请假人", type: "object", trigger: ["blur", "change"] }],
                poi_address: [{ required: true, message: "请选择请假地址", type: "string", trigger: ["blur", "change"] }],
                lat: [{ required: true, message: "获取定位信息出错", type: "string", trigger: ["blur", "change"] }],
                lng: [{ required: true, message: "获取定位信息出错", type: "string", trigger: ["blur", "change"] }],
                start_date: [{ required: true, message: "请选择开始时间", type: "string", trigger: ["blur", "change"] }],
                end_date: [{ required: true, message: "请选择结束时间", type: "string", trigger: ["blur", "change"] }],
                leave_type: [{ required: true, message: "请选择请假类型", type: "object", trigger: ["blur", "change"] }],
                go_time: [{ required: true, message: "请选择离开时间", type: "string", trigger: ["blur", "change"] }],
                destination: [{ required: true, message: "请输入到达地点", type: "string", trigger: ["blur", "change"] }],
            },
        };
    },
    computed: {
        approverChunks() {
            // 按approve_node排序
            const sortedList = [...this.approve_list].sort((a, b) => parseFloat(a.approve_node) - parseFloat(b.approve_node));

            // 每3个一组进行分割
            const chunks = [];
            for (let i = 0; i < sortedList.length; i += 3) {
                chunks.push(sortedList.slice(i, i + 3));
            }
            return chunks;
        },
    },
    onReady() {
        if (this.$refs.uForm) {
            this.$refs.uForm.setRules(this.rules);
        }
    },
    onLoad(options) {
        this.handleGetLocation();
    },
    onShow() {
        this.$nextTick(() => {
            this.handleGetLeaveList();
        });
    },
    methods: {
        handleTab(type) {
            this.currentTab = type;
            if (type == "self") {
                this.rules.user_id[0].required = false;
                this.rules.poi_address[0].required = false;
            } else {
                this.rules.user_id[0].required = true;
                this.rules.poi_address[0].required = false;
            }
            this.$refs.uForm.setRules(this.rules);
        },
        handleUpdateFile(file) {
            this.form.images = file;
        },
        // 确认请假类型
        confirmChooseLeaveType(e) {
            console.log("选择的请假类型:", e);
            if (e && e.value && e.value[0]) {
                this.form.leave_type = e.value[0];
                this.shwoLeaveTypePicker = false;
            } else {
                this.$fn.showToast("请假类型选择错误");
            }
        },
        // 获取请假相关数据
        async handleGetLeaveList() {
            try {
                const res = await this.$api.getLeaveData();
                if (res.code === 200) {
                    this.leaveData = res.data;
                    this.leave_types = res.data.leave_type || [];
                    this.approve_list = res.data.approve_list || [];
                    this.village_user_list = res.data.village_user_list || [];
                    this.personOnLeaveList = this.village_user_list.map((item) => ({ label: item.nickname, value: item.id }));
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取请假列表失败");
            }
        },
        // 获取当前定位
        async handleGetLocation() {
            uni.showLoading({
                title: "获取定位中...",
            });
            try {
                const res = await this.$fn.getLocation();
                if (res && res.latitude && res.longitude) {
                    this.form.lat = res.latitude;
                    this.form.lng = res.longitude;
                    // 获取地名
                    await this.handleGetLocationName(`${res.latitude},${res.longitude}`);
                } else {
                    uni.showModal({
                        title: "提示",
                        content: "获取定位失败，是否重试？",
                        showCancel: true,
                        success: ({ confirm, cancel }) => {
                            if (confirm) {
                                this.handleGetLocation();
                            } else {
                                uni.navigateBack();
                            }
                        },
                    });
                }
            } catch (error) {
                console.error("获取定位失败:", error);
                uni.showModal({
                    title: "提示",
                    content: "获取定位失败，是否重试？",
                    showCancel: true,
                    success: ({ confirm, cancel }) => {
                        if (confirm) {
                            this.handleGetLocation();
                        } else {
                            uni.navigateBack();
                        }
                    },
                });
            } finally {
                uni.hideLoading();
            }
        },

        // 获取当前地名
        async handleGetLocationName(location) {
            try {
                const res = await this.$fn.getLocationName(
                    "U2KBZ-NDELN-R2CF5-SDW3U-5QPI7-SXFI4",
                    location,
                    "4wXlpKbn0V8GQYCDaTLdv07bowPYt5x5"
                );
                if (res.statusCode === 200) {
                    this.form.province = res.data.result.ad_info.province;
                    this.form.city = res.data.result.ad_info.city;
                    this.form.poi_address = res.data.address;
                } else {
                    this.$fn.showToast("获取地名失败");
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取地名失败");
            }
        },
        // 确认时间
        confirmChooseDate(e) {
            this.form[this.currentDateType] = this.$u.timeFormat(e.value, this.dateMode == "datetime" ? "yyyy-mm-dd hh:MM" : "yyyy-mm-dd");
            this.showDateChoosePicker = false;
        },
        // 选择请假人
        handleSelectPersonOnLeave(item) {
            console.log(item);

            this.choosePersonOnLeave = item.value[0];
            this.form.user_id = item.value[0];
            this.showPersonOnLeavePopup = false;
        },
        // 选择时间
        handleChooseDate(type) {
            this.currentDateType = type;
            this.dateMode = type == "go_time" ? "datetime" : "date";
            this.showDateChoosePicker = true;
        },
        // 代他人请假 地图选点
        async handleChooseLocation() {
            try {
                const res = await this.$fn.chooseLocation();
                if (res && res.errMsg == "chooseLocation:ok") {
                    this.form.poi_address = `${res.address}${res.name}`;
                    this.form.lat = res.latitude;
                    this.form.lng = res.longitude;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("地图选点失败");
            }
        },
        // 搜索用户列表
        async handleSearchUserlist() {
            try {
                uni.showLoading({
                    title: "搜索中...",
                    mask: true,
                });
                const res = await this.$api.getUserList({ keywords: this.keywords });
                if (res.code === 200) {
                    this.personOnLeaveList = res.data.list.map((item) => ({ label: item.nickname, value: item.id }));
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("搜索用户列表失败");
            } finally {
                uni.hideLoading();
            }
        },
        // 提交
        async handleSubmit() {
            try {
                const result = await this.$refs.uForm.validate();
                if (result) {
                    let res;
                    if (this.currentTab == "self") {
                        res = await this.$api.leaveApplication({
                            ...this.form,
                            images: this.form.images ? this.form.images.join(",") : "",
                            leave_type: this.form.leave_type.value,
                        });
                    } else {
                        res = await this.$api.leaveApplicationOther({
                            ...this.form,
                            leave_type: this.form.leave_type.value,
                            user_id: this.form.user_id.value,
                        });
                    }
                    if (res.code === 200) {
                        this.$fn.showToast("提交成功");
                        setTimeout(() => {
                            this.$fn.jumpBack();
                        }, 1000);
                    }
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("提交时发送错误");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    padding-bottom: calc(env(safe-area-inset-bottom) + 124rpx);

    .tabs-box {
        padding: 24rpx 24rpx 0 24rpx;

        .tabs {
            height: 64rpx;

            .tab-item {
                height: 64rpx;
                color: #666666;
            }

            .active {
                color: #fff;
                background: #fd0100;
            }
        }
    }

    .content {
        padding: 24rpx;

        .form {
            background-color: #fff;
            border-radius: 24rpx;

            .textarea-box {
                margin-top: 24rpx;
                width: 100%;

                ::v-deep .u-textarea {
                    background-color: #f7f8fa;
                }

                ::v-deep .u-textarea__count {
                    background-color: #f7f8fa !important;
                }
            }

            .upload-box {
                margin-top: 24rpx;
            }
        }
    }

    .line {
        width: 100%;
        height: 10rpx;
        background: #f2f4f7;
    }

    .process {
        padding: 32rpx 24rpx;
        position: relative;

        .process-list {
            margin-top: 20rpx;

            .process-item {
                .avatar {
                    width: 128rpx;
                    height: 128rpx;
                    border-radius: 16rpx;
                    position: relative;

                    .avatar-img {
                        width: 128rpx;
                        height: 128rpx;
                    }

                    .avatar-text {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        text-align: center;
                        background: #ebb84c;
                        border-radius: 0 0 16rpx 16rpx;
                    }
                }
            }
        }

        .steps {
            margin-top: 20rpx;
            z-index: 10;
            position: relative;

            .step-item {
                width: 128rpx;
                margin-right: 3rpx; // 微调位置

                .step-item-text {
                    width: 35rpx;
                    height: 35rpx;
                    border-radius: 50%;
                    background: #fd0100;
                    text-align: center;
                    color: #fff;
                }
            }

            .step-line {
                position: absolute;
                bottom: 17rpx;
                left: 0;
                right: 0;
                width: 100%;
                height: 2rpx;
                z-index: -1;
                border-bottom: 2rpx dashed #00000060;
            }
        }

        .process-btn {
            margin-top: 128rpx;

            .btn {
                width: 702rpx;
                height: 96rpx;
                background: #fd0100;
                border-radius: 12rpx;
            }
        }
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: #fbfbfb;
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
        z-index: 999;

        .btn {
            width: 100%;
            height: 100%;
            background: #fd0100;
            border-radius: 12rpx;
        }
    }

    .passenger-popup {
        padding: 32rpx 24rpx 24rpx 24rpx;
        background: #fff;
        border-radius: 24rpx 24rpx 0 0;
        height: 50vh;

        .search-box {
            padding: 20rpx 0;
        }
    }
}
</style>
