import { genTestUserSig } from "@/common/js/TUICallKit/GenerateTestUserSig.js";

// const userInfo = uni.getStorageSync("userInfo");
// const userID = String(userInfo.id);
// const { userSig, sdkAppID: SDKAppID } = genTestUserSig(userID);

// 腾讯云IM即时通信
import TencentCloudChat from "@tencentcloud/chat";
import { TUILogin } from "@tencentcloud/tui-core";
import { TUIChatKit } from "../TUIKit";


async function initTUICallKit() {
    // 将用户信息和userSig的生成逻辑移入函数内部，确保在调用时获取
    const userInfo = uni.getStorageSync("userInfo");
    if (!userInfo || !userInfo.id) {
        console.error("【init】IM/音视频通信: 无法获取用户信息，初始化中止。请确保用户已登录。");
        return;
    }
    const userID = String(userInfo.id);
    const { userSig, sdkAppID: SDKAppID } = genTestUserSig(userID);

    uni.$TUICallKit = uni.requireNativePlugin("TencentCloud-TUICallKit"); // 腾讯云音视频插件;
    uni.$TUILogin = TUILogin; // 腾讯云IM登录
    uni.$TUIChatKit = TUIChatKit; // 腾讯云IM插件
    console.log("sdkAppID", SDKAppID);

    // 设置来电横幅
    uni.$TUICallKit.enableIncomingBanner(true);
    // 设置悬浮窗功能
    uni.$TUICallKit.enableFloatWindow(true);
    uni.$SDKAppID = SDKAppID; // Your SDKAppID
    uni.$userID = userID; // Your userID
    uni.$userSig = userSig; // Your userSig

    uni.$chat = TencentCloudChat.create({ SDKAppID: uni.$SDKAppID });
    uni.$chat.setLogLevel(3);
    handleIMInit() // IM初始化
    await handleIMLogin(); // IM即时通讯登录
    await handleLogin(); // 音视频登录
}

// IM初始化 
function handleIMInit() {
    // 初始化TUIChatKit
    uni.$TUIChatKit.init();
}


// IM登录
async function handleIMLogin() {
    try {
        const loginParams = {
            SDKAppID: uni.$SDKAppID,
            userID: uni.$userID,
            userSig: uni.$userSig,
            useUploadPlugin: true,
            framework: `vue2`,
        }
        // IM登录
        const res = await uni.$TUILogin.login(loginParams)
        if (res.code === 0) {
            console.log(`【init】IM登录成功`, res);
            // 登录成功后，TUIChatKit 会持有 tim 实例
            // 发出全局事件，通知 App.vue 等模块 IM 已准备就绪
            if (uni.$TUIChatKit && uni.$TUIChatKit.tim) {
                uni.$emit('im-ready', { tim: uni.$TUIChatKit.tim, TIM: uni.$TUIChatKit.TIM });
            }
        }


    } catch (error) {
        console.error("【init】IM登录失败", error);
        uni.showToast({
            title: '【init】IM登录失败',
            icon: 'none',
            mask: true
        })
    }

}

// 音视频登录
async function handleLogin() {
    const loginParams = { SDKAppID: uni.$SDKAppID, userID: uni.$userID, userSig: uni.$userSig }; // apply SDKAppID、userSig
    //【2】Login
    uni.$TUICallKit.login(loginParams, async (res) => {
        if (res.code === 0) {
            console.log(`【init】音视频登录成功`, res);
            await handleSetSelfInfo();
        } else {
            console.error("【init】音视频登录失败：", res.msg, loginParams);
        }
    });
}
// 设置用户信息
async function handleSetSelfInfo() {
    const userInfo = uni.getStorageSync("userInfo");
    const options = {
        userID: userInfo.id,
        nickName: userInfo.nickname,
        avatar: userInfo.avatar,
    };
    await uni.$TUICallKit.setSelfInfo(options, (res) => {
        if (res.code === 0) {
            console.log("【init】IM/音视频通信：设置用户信息成功", res);
        } else {
            console.error(`【init】IM/音视频通信：设置用户信息失败： ${res.msg}`, res);
        }
    });
}

export { initTUICallKit };