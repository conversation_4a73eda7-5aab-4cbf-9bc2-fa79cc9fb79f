{"name": "@tencentcloud/tui-customer-service-plugin", "version": "2.2.6", "description": "chat uikit tui-customer-service-plugin", "main": "index", "keywords": ["uikit", "chat", "uni-app", "IM", "tencent", "tencentcloud", "messaging", "即时通信", "通信", "WebSocket"], "dependencies": {"@tencentcloud/universal-api": "latest", "@vue/composition-api": "^1.0.0-rc.1", "marked": "4.0.0"}, "peerDependencies": {"@tencentcloud/tui-core": "latest", "@vue/composition-api": "^1.0.0-rc.1", "vue": "^2.0.0 || >=3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "author": "", "license": "ISC"}