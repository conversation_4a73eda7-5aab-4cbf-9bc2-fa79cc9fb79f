<template>
    <!-- 注册 -->
    <view>
        <c-navBar title="注册" isSeat isPerch :isBack="true"></c-navBar>
        <view class="tac">
            <view class="register-title">
                <view class="title-bar"></view>
                <view class="title-text">注册账号</view>
            </view>
            <view class="form-container">
                <view class="input-box">
                    <input type="text" placeholder="请输入手机号" v-model="phoneNumber" />
                </view>
                <view class="input-box">
                    <input type="password" placeholder="请设置登录密码" v-model="password" />
                </view>
                <view class="input-box">
                    <input type="password" placeholder="请再次输入登录密码" v-model="confirmPassword" />
                </view>
                <view class="input-box code-box">
                    <input type="text" placeholder="请输入验证码" v-model="verifyCode" />
                    <view class="code-btn" @click="getVerifyCode">获取验证码</view>
                </view>

                <button class="register-btn" @click="register">注册</button>
            </view>
            <view class="login-link"> 已有账号？<text class="login-text" @click="toLogin">去登录</text> </view>
            <view class="agreement-box">
                <view class="ag-box" @click="tabAgree">
                    <image v-if="isAgree" src="/static/common/select.png" mode=""></image>
                    <image v-else src="/static/common/noSelect.png" mode=""></image>
                </view>
                <view class="agreement">
                    <text @click.stop="tabAgree">我已阅读并同意</text>
                    <text class="primary-color" @click.stop="jumpXy(1)">《用户协议》</text>
                    <text class="primary-color" @click.stop="jumpXy(2)">《隐私协议》</text>
                </view>
            </view>
        </view>
        <view class="u_modal" v-if="modalShow">
            <view class="shadow">
                <img src="/static//common/alert.png" alt="" class="logImg" />
                <view class="alers">注册成功</view>
                <view class="btns">
                    <view class="logCancel" @click="cancelT">取消</view>
                    <view class="logOk" @click="confirmT">确认</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            phoneNumber: "",
            password: "",
            confirmPassword: "",
            verifyCode: "",
            isAgree: false, //是否同意协议
            modalShow: false,
        };
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },

        // 获取验证码
        getVerifyCode() {
            if (!this.phoneNumber) {
                uni.showToast({
                    title: "请输入手机号",
                    icon: "none",
                });
                return;
            }

            // 此处调用获取验证码接口
            uni.showToast({
                title: "验证码已发送",
                icon: "none",
            });
        },

        // 注册
        register() {
            if (!this.phoneNumber || !this.password || !this.confirmPassword || !this.verifyCode) {
                uni.showToast({
                    title: "请填写完整信息",
                    icon: "none",
                });
                return;
            }

            if (this.password !== this.confirmPassword) {
                uni.showToast({
                    title: "两次输入的密码不一致",
                    icon: "none",
                });
                return;
            }

            if (!this.isAgree) {
                uni.showToast({
                    title: "请先同意用户协议",
                    icon: "none",
                });
                return;
            }
            this.modalShow = true;
            // 此处调用注册接口
            // uni.showToast({
            // 	title: '注册成功',
            // 	icon: 'none',
            // 	success: () => {
            // 		setTimeout(() => {
            // 			this.toLogin();
            // 		}, 1500);
            // 	}
            // });
        },

        // 去登录页面
        toLogin() {
            uni.redirectTo({
                url: "/pages/tabbar/login",
            });
        },

        // 跳转协议
        jumpXy(e) {
            uni.navigateTo({
                url: "/pages/tabbar/agreement?type=" + e,
            });
        },

        // 同意/不同意协议
        tabAgree() {
            this.isAgree = !this.isAgree;
        },
    },
};
</script>

<style lang="scss" scoped>
.register-title {
    margin-top: 136rpx;
    margin-left: 80rpx;
    position: relative;
    .title-bar {
        position: absolute;
        bottom: 0;
        left: -2rpx;
        width: 160rpx;
        height: 20rpx;
        background: linear-gradient(315deg, #ff0036 20%, #ff5975 100%), #ff5975;
        border-radius: 48rpx;
    }

    .title-text {
        position: absolute;
        bottom: 5rpx;
        left: 0rpx;
        font-weight: bold;
        font-size: 40rpx;
        color: #000000;
    }
}
.form-container {
    padding: 84rpx 50rpx 0 50rpx;
}

.input-box {
    background-color: #f5f5f5;
    height: 100rpx;
    margin: 0 30rpx;
    padding: 0 28rpx;
    margin-bottom: 48rpx;
    border-radius: 12rpx;
    text-align: left;
    // display: flex;
    // align-items: center;

    input {
        color: #8c9299;
        // flex: 1;
        height: 100%;
        font-size: 24rpx;
    }
}

.code-box {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .code-btn {
        font-weight: 500;
        color: #ec3015;
        font-size: 28rpx;
        padding-left: 20rpx;
    }
}

.register-btn {
    margin-top: 80rpx;
    width: 100%;
    height: 96rpx;
    background: linear-gradient(315deg, #ec3015 0%, #ff0036 100%);
    border-radius: 48rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    font-size: 36rpx;
    color: #ffffff;
}

.login-link {
    margin-top: 40rpx;
    color: #666;
    font-size: 28rpx;

    .login-text {
        color: #ea403a;
    }
}

.agreement-box {
    position: fixed;
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    .ag-box {
        width: 50rpx;
        height: 50rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    image {
        width: 24rpx;
        height: 24rpx;
    }

    .agreement {
        font-size: 28rpx;
        font-weight: 500;
        color: #666666;

        .primary-color {
            color: #e6212a;
        }
    }
}

.u_modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    justify-content: center;
    align-items: center;
    z-index: 999;

    .shadow {
        width: 480rpx;
        height: 520rpx;
        background: #ffffff;
        border-radius: 20rpx;
        position: relative;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;

        .logImg {
            width: 176rpx;
            height: 176rpx;
        }

        .alers {
            font-family: PingFang SC, PingFang SC;
            font-weight: bold;
            font-size: 32rpx;
            color: #1a1a1a;
            line-height: 38rpx;
            text-align: center;
            font-style: normal;
            text-transform: none;
        }

        .btns {
            display: flex;
            align-items: center;
            justify-content: space-around;

            .logCancel {
                width: 176rpx;
                height: 60rpx;
                //  border-radius: 6rpx;
                border: 1rpx solid #333333;
                text-align: center;
                line-height: 64rpx;
                font-weight: 500;
                font-size: 28rpx;
                color: #000;
                font-style: normal;
                text-transform: none;
                margin-right: 20rpx;
            }

            .logOk {
                width: 176rpx;
                height: 64rpx;
                //   border-radius: 6rpx;
                text-align: center;
                line-height: 64rpx;
                width: 176rpx;
                background: #e6212a;
                font-weight: 500;
                font-size: 28rpx;
                color: #ffffff;
                font-style: normal;
                text-transform: none;
            }
        }
    }
}
</style>
