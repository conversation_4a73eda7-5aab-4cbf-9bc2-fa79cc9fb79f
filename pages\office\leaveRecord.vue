<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="请假记录" :isBack="true"> </c-navBar>
        </section>
        <section class="tabs">
            <u-tabs
                :list="tabs"
                activeStyle="color: #FD0100; font-weight: bold;"
                lineColor="#FD0100"
                :scrollable="false"
                @click="handleClick"
            ></u-tabs>
        </section>
        <section class="list" v-if="filterList.length > 0">
            <scroll-view scroll-y style="height: calc(100vh - 300rpx)">
                <view
                    class="list-item flex align-start justify-between"
                    v-for="item in filterList"
                    :key="item.id"
                    @click="this.$fn.jumpPage(`/pages/office/leaveDetail?id=${item.id}`)"
                >
                    <view class="left flex flex-col gap-16">
                        <view class="flex align-center gap-22">
                            <text calss="mf-font-32 mf-weight-bold" style="color: #333">
                                {{ item.nickname }}{{ item.leave_type ? item.leave_type.name : "" }}
                            </text>
                            <view class="status mf-font-20" style="color: #fff">
                                {{ item.approve_status ? item.approve_status.name : "" }}
                            </view>
                        </view>
                        <view class="flex flex-col gap-8 mf-font-24" style="color: #999999">
                            <text>开始时间：{{ item.start_date }}</text>
                            <text>结束时间：{{ item.end_date }}</text>
                            <text>申请人：{{ item.nickname }}</text>
                            <text>申请时间：{{ item.create_time }}</text>
                        </view>
                    </view>
                    <view class="right">
                        <u-image
                            src="/static/common/status1.png"
                            v-if="item.approve_status.value === 1"
                            width="120rpx"
                            height="120rpx"
                            mode="aspectFill"
                        ></u-image>
                        <u-image
                            src="/static/common/status2.png"
                            v-if="status === 2"
                            width="120rpx"
                            height="120rpx"
                            mode="aspectFill"
                        ></u-image>
                        <u-image
                            src="/static/common/status3.png"
                            v-if="status === 3"
                            width="120rpx"
                            height="120rpx"
                            mode="aspectFill"
                        ></u-image>
                        <u-image
                            src="/static/common/status4.png"
                            v-if="item.approve_status.value === 0"
                            width="120rpx"
                            height="120rpx"
                            mode="aspectFill"
                        ></u-image>
                    </view>
                </view>
            </scroll-view>
        </section>
        <section class="empty flex flex-center" v-else>
            <u-empty text="暂无记录" />
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            type: "leave",
            status: -1,
            tabs: [
                {
                    name: "全部",
                    value: -1,
                },
                {
                    name: "待审批",
                    value: 0,
                },
                {
                    name: "已同意",
                    value: 1,
                },
                {
                    name: "已驳回",
                    value: 2,
                },
                {
                    name: "已查阅",
                    value: 3,
                },
            ],
            list: [],
            filterList: [],
            userInfo: {},
        };
    },
    onLoad(options) {
        this.userInfo = uni.getStorageSync("userInfo");
        this.handleGetLeaveList();
    },
    methods: {
        handleClick(tab) {
            if (tab.value === -1) {
                this.handleGetLeaveList();
            } else {
                this.filterList = this.list.filter((item) => item.approve_status && item.approve_status.value === tab.value);
            }
        },
        // 获取请假记录
        async handleGetLeaveList() {
            try {
                const res = await this.$api.leaveUserList({
                    user_id: this.type === "leave" ? this.userInfo.id : "",
                });
                if (res.code === 200) {
                    this.list = res.data.list;
                    this.filterList = res.data.list;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取请假记录失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .tabs {
        background: #fff5f5;
    }
    .list {
        padding: 24rpx;
        background-color: #fbfbfb;
        .list-item {
            padding: 24rpx 20rpx;
            background: #ffffff;
            border-radius: 12rpx;
            margin-top: 24rpx;
            &:first-child {
                margin-top: 0;
            }
            .left {
                .status {
                    padding: 4rpx 10rpx;
                    background: #fd0100;
                    border-radius: 12rpx 0rpx 12rpx 0rpx;
                }
            }
        }
    }
    .empty {
        height: calc(100vh - 300rpx);
        background-color: #fbfbfb;
    }
}
</style>
