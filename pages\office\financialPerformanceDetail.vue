<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="资金绩效" isPerch isBack> </c-navBar>
        </section>
        <section class="content">
            <u--form
                labelPosition="left"
                :model="form"
                :rules="rules"
                ref="uForm"
                errorType="toast"
                labelWidth="220rpx"
                :labelStyle="{ color: '#1a1a1a', fontWeight: 'bold' }"
            >
                <view class="form">
                    <u-form-item label="申报人" prop="applyUsername" borderBottom>
                        <u-input
                            v-model="form.applyUsername"
                            disabled
                            disabledColor="transparent"
                            inputAlign="right"
                            suffixIcon="arrow-right"
                            border="none"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            placeholder="请选择申报人"
                        />
                    </u-form-item>
                    <u-form-item label="申报单位" prop="affiliated_unit" borderBottom>
                        <u-input
                            v-model="form.affiliated_unit"
                            disabled
                            disabledColor="transparent"
                            inputAlign="right"
                            suffixIcon="arrow-right"
                            border="none"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            placeholder="请选择申报单位"
                        />
                    </u-form-item>
                    <u-form-item label="项目启动时间" prop="start_date" borderBottom @click="handleChooseDate('start_date')">
                        <u-input
                            :value="form.start_date ? $u.timeFormat(form.start_date, 'yyyy-mm-dd') : ''"
                            disabled
                            disabledColor="transparent"
                            inputAlign="right"
                            suffixIcon="arrow-right"
                            border="none"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            placeholder="请选择项目启动时间"
                        />
                    </u-form-item>
                    <u-form-item label="项目截止时间" prop="end_date" @click="handleChooseDate('end_date')">
                        <u-input
                            :value="form.end_date ? $u.timeFormat(form.end_date, 'yyyy-mm-dd') : ''"
                            disabled
                            disabledColor="transparent"
                            inputAlign="right"
                            suffixIcon="arrow-right"
                            border="none"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            placeholder="请选择项目截止时间"
                        />
                    </u-form-item>
                </view>
                <view class="line"></view>
                <view class="form two">
                    <u-form-item label="经费类别" prop="type" borderBottom @click="showPicker = true">
                        <u-input
                            v-model="form.type.type_name"
                            disabled
                            disabledColor="transparent"
                            inputAlign="right"
                            suffixIcon="arrow-right"
                            border="none"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            placeholder="请输入经费类别"
                        />
                    </u-form-item>
                    <u-form-item label="项目总金额" prop="total_amount" borderBottom>
                        <u-input
                            v-model="form.total_amount"
                            inputAlign="right"
                            border="none"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            placeholder="请输入项目总金额"
                        />
                    </u-form-item>
                    <u-form-item label="已支出金额" prop="use_amount" borderBottom>
                        <u-input
                            v-model="form.use_amount"
                            inputAlign="right"
                            border="none"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            placeholder="请输入已支出金额"
                        />
                    </u-form-item>
                    <u-form-item label="项目内容" prop="project_content" labelPosition="top">
                        <view class="textarea-box">
                            <u--textarea
                                v-model="form.project_content"
                                placeholderStyle="color: #999"
                                count
                                border="none"
                                height="300rpx"
                                maxlength="80"
                                placeholder="请输入项目内容"
                            ></u--textarea>
                        </view>
                    </u-form-item>
                    <u-form-item label="项目进度" prop="project_progress" labelPosition="top">
                        <view class="textarea-box">
                            <u--textarea
                                v-model="form.project_progress"
                                placeholderStyle="color: #999"
                                count
                                border="none"
                                height="300rpx"
                                maxlength="80"
                                placeholder="请输入项目进度"
                            ></u--textarea>
                        </view>
                    </u-form-item>
                    <u-form-item label="效益及分红机制" prop="project_benefit" labelPosition="top">
                        <view class="textarea-box">
                            <u--textarea
                                v-model="form.project_benefit"
                                placeholderStyle="color: #999"
                                count
                                border="none"
                                height="300rpx"
                                maxlength="80"
                                placeholder="请输入项目内容"
                            ></u--textarea>
                        </view>
                    </u-form-item>
                </view>
            </u--form>
        </section>
        <section class="footer" @touchmove.stop.prevent>
            <view class="btn flex flex-center mf-font-36 mf-weight-bold" style="color: #fff" @click.stop.prevent="handleSubmit">提交</view>
        </section>

        <u-datetime-picker
            :show="showDate"
            :value="currentDate"
            close-on-click-overlay
            @confirm="handleConfirmDate"
            @close="showDate = false"
            @cancel="showDate = false"
            mode="date"
        ></u-datetime-picker>
        <u-picker
            :show="showPicker"
            key-name="type_name"
            :columns="[funds_type]"
            @confirm="handleConfirmPicker"
            @close="showPicker = false"
            @cancel="showPicker = false"
            close-on-click-overlay
        ></u-picker>
    </view>
</template>

<script>
export default {
    data() {
        return {
            showDate: false, // 显示日期选择器
            showPicker: false, // 显示选择器
            minDate: "", // 最小日期
            maxDate: "", // 最大日期
            currentDate: new Date().getTime(), // 当前选择的日期
            currentDateType: "", // 当前选择的日期类型
            funds_type: [], // 经费类别
            form: {
                applyUsername: "", // 申报人
                end_date: "", // 项目截止时间
                affiliated_unit: "", // 申报单位
                type: "", // 经费类别
                total_amount: "", // 项目总金额
                level: 3, // 级别(不知道干啥的)
                use_amount: "", // 已支出金额
                project_content: "", // 项目内容
                project_progress: "", // 项目进度
                project_benefit: "", // 效益及分红机制
                parent_type: "", // 父类型
                start_date: "", // 项目启动时间
            },
            rules: {
                applyUsername: { required: true, type: "string", message: "请输入申报人", trigger: "blur" },
                affiliated_unit: { required: true, type: "string", message: "请输入申报单位", trigger: "blur" },
                type: { required: true, type: "object", message: "请输入经费类别", trigger: "blur" },
                total_amount: { required: true, type: "number", message: "请输入项目总金额", trigger: "blur" },
                use_amount: { required: true, type: "number", message: "请输入已支出金额", trigger: "blur" },
                project_content: { required: true, type: "string", message: "请输入项目内容", trigger: "blur" },
                project_progress: { required: true, type: "string", message: "请输入项目进度", trigger: "blur" },
            },
            applyFundsInfo: {}, // 申请经费相关信息
        };
    },
    onReady() {
        this.$refs.uForm.setRules(this.rules);
    },
    onLoad(options) {
        this.handleGetApplyFundsInfo(options.parent_type);
        this.form.parent_type = options.parent_type;
    },
    methods: {
        // 日期选择
        handleChooseDate(type) {
            this.showDate = true;
            this.currentDateType = type;
        },
        // 确认日期选择
        handleConfirmDate(date) {
            this.form[this.currentDateType] = this.$u.timeFormat(date.value, "yyyy-mm-dd");
            this.showDate = false;
        },
        // 经费类别选择
        handleConfirmPicker(value) {
            console.log(value);
            this.form.type = value.value[0];
            this.showPicker = false;
        },
        // 获取申请经费相关信息
        async handleGetApplyFundsInfo(parent_type) {
            try {
                const res = await this.$api.preApplyFundsData({ parent_type });
                if (res.code === 200) {
                    this.applyFundsInfo = res.data;
                    this.form.applyUsername = res.data.apply_username;
                    this.form.affiliated_unit = res.data.apply_unit;
                    this.funds_type = res.data.funds_type;
                }
            } catch (error) {
                console.log(error);
                this.$fn.showToast("获取申请经费相关信息失败");
            }
        },
        // 提交
        handleSubmit() {
            console.log(this.$refs.uForm);
            this.$refs.uForm.validate().then(async (valid) => {
                if (valid) {
                    try {
                        const res = await this.$api.fundsApply({
                            ...this.form,
                            type: this.form.type.type,
                        });
                        if (res.code === 200) {
                            this.$fn.showToast("提交成功");
                            setTimeout(() => {
                                this.$fn.jumpBack();
                            }, 1000);
                        }
                    } catch (error) {
                        console.error(error);
                        this.$fn.showToast("资金绩效申请失败");
                    }
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        padding-bottom: 164rpx;
        .form {
            padding: 24rpx 24rpx 0 24rpx;
            background-color: #fff;
            border-radius: 24rpx;

            &.two {
                padding: 0 24rpx 24rpx 24rpx;
                .textarea-box {
                    margin-top: 24rpx;
                    ::v-deep .u-textarea {
                        background-color: #f7f8fa;
                    }
                    ::v-deep .u-textarea__count {
                        background-color: #f7f8fa !important;
                    }
                }
            }
        }
        .line {
            width: 100%;
            height: 10rpx;
            background: #f2f4f7;
        }
    }
    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: #fbfbfb;
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
        z-index: 999;
        .btn {
            width: 100%;
            height: 100%;
            background: #fd0100;
            border-radius: 12rpx;
        }
    }
}
</style>
