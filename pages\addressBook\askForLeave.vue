<template>
    <view class="view">
        <section class="groups" v-if="list.length > 0" style="height: calc(100vh - 390rpx)">
            <view class="group-item flex align-center gap-24" v-for="(item, index) in list" :key="index">
                <view class="user flex-1 flex align-center justify-between">
                    <view class="flex align-center gap-24">
                        <view class="user-avatar">
                            <u-image
                                :src="item.avatar || '/static/common/group-icon2.png'"
                                width="128rpx"
                                height="128rpx"
                                radius="16rpx"
                                mode="scaleToFill"
                            ></u-image>
                            <view class="user-status mf-font-20" style="color: #fff">
                                {{ item.online_status ? item.online_status.name : "获取失败" }}
                            </view>
                        </view>
                        <view class="flex flex-col gap-30">
                            <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ item.nickname }}</view>
                            <view class="mf-font-28" style="color: #ebb84c">{{ item.role ? item.role.role_name : "获取失败" }}</view>
                        </view>
                    </view>
                    <view class="action-box">
                        <view class="btn mf-font-28" style="color: #1a1a1a" @click.stop="handleClickMessage(item)"> 发私信 </view>
                    </view>
                </view>
            </view>
        </section>
        <section class="empty flex flex-center" style="height: 300rpx" v-else>
            <u-empty mode="list" icon="list" />
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            list: [],
        };
    },
    onLoad() {},
    methods: {
        // 获取请假记录
        async handleGetLeaveList() {
            try {
                const res = await this.$api.leaveUserList();
                if (res.code === 200) {
                    this.list = res.data.list;
                    console.log(this.list, "list");

                    this.$emit("askForLeaveDataNum", this.list.length);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取请假记录失败");
            }
        },
        // 发送私信
        handleClickMessage(item) {
            // #ifndef APP-PLUS
            this.$fn.showToast("请前往APP使用");
            return;
            // #endif
            this.$fn.jumpPage(`/TUIKit/components/TUIChat/index?conversationID=C2C${item.user_id}`);
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    // background: #fbfbfb;

    .groups {
        padding: 24rpx;
        background-color: #fbfbfb;
        .back-item {
            padding: 30rpx 32rpx;
            background: #fff;
            margin-bottom: 10rpx;
            border-radius: 12rpx;
        }

        .group-item {
            padding: 40rpx 32rpx;
            border-radius: 12rpx;
            background: #fff;
            margin-bottom: 10rpx;
            &:last-child {
                margin-bottom: 0;
            }

            .group {
                display: flex;
                align-items: center;
                gap: 24rpx;
                width: 100%;
            }

            .user {
                .user-avatar {
                    position: relative;
                    .user-status {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        background: #fd0100;
                        border-radius: 16rpx 0rpx 16rpx 0rpx;
                        padding: 4rpx 8rpx;
                    }
                }
                .action-box {
                    .btn {
                        padding: 16rpx 22rpx;
                        border-radius: 8rpx;
                        border: 1rpx solid #e6e6e6;
                    }
                }
            }
        }
    }
}
</style>
