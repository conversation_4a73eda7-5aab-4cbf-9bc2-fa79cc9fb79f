<template>
    <view class="view">
        <section class="list" v-if="list.length > 0" style="height: calc(100vh - 390rpx)">
            <view class="list-item flex align-start justify-between" v-for="item in list" :key="item.id" @click="handleGetReportDetail(item)">
                <view class="left flex align-start gap-24">
                    <view class="user-avatar">
                        <u-image
                            :src="item.avatar || '/static/mine/avatar.png'"
                            width="128rpx"
                            height="128rpx"
                            radius="16rpx"
                            mode="aspectFill"
                        ></u-image>
                        <view class="user-status mf-font-20" style="color: #fff">在岗</view>
                    </view>
                    <view class="flex flex-col gap-16">
                        <view class="flex align-center gap-22">
                            <text calss="mf-font-32 mf-weight-bold" style="color: #333">
                                {{ item.nickname }}{{ item.type ? item.type.name : "未知状态" }}
                            </text>
                            <view class="status mf-font-20" style="color: #fff">{{ item.type ? item.type.name : "未知状态" }}</view>
                        </view>
                        <view class="flex flex-col gap-8 mf-font-24" style="color: #999999">
                            <text>开始时间：{{ item.start_date }}</text>
                            <text>结束时间：{{ item.end_date }}</text>
                            <text>申请人：{{ item.nickname }}</text>
                            <text>申请时间：{{ item.create_time }}</text>
                        </view>
                    </view>
                </view>
                <view class="right" v-if="item.approve_status">
                    <u-image
                        src="/static/common/status1.png"
                        v-if="item.approve_status.value == 1"
                        width="120rpx"
                        height="120rpx"
                        mode="aspectFill"
                    ></u-image>
                    <u-image
                        src="/static/common/status2.png"
                        v-if="item.approve_status.value == 2"
                        width="120rpx"
                        height="120rpx"
                        mode="aspectFill"
                    ></u-image>
                    <u-image
                        src="/static/common/status3.png"
                        v-if="item.approve_status.value == 3"
                        width="120rpx"
                        height="120rpx"
                        mode="aspectFill"
                    ></u-image>
                    <u-image
                        src="/static/common/status4.png"
                        v-if="item.approve_status.value == 0"
                        width="120rpx"
                        height="120rpx"
                        mode="aspectFill"
                    ></u-image>
                </view>
            </view>
            <!-- </c-scroll-list> -->
        </section>
        <section class="empty flex flex-center" style="height: 300rpx" v-else>
            <u-empty mode="list" icon="list" />
        </section>
    </view>
</template>

<script>
export default {
    name: "reporter",
    data() {
        return {
            status: 1,
            list: [],
        };
    },
    methods: {
        // 获取报备列表
        async handleGetReportUserList() {
            try {
                const res = await this.$api.getReportUserList();
                if (res.code === 200) {
                    this.list = res.data.list;
                    this.$emit("reporterDataNum", this.list.length);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取报备列表失败");
            }
        },
        // 前往报备详情
        handleGetReportDetail(item) {
            this.$fn.jumpPage(`/pages/office/applyDetail?id=${item.id}&type=${item.type ? item.type.name : "unknown"}`);
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .list {
        padding: 24rpx;
        background-color: #fbfbfb;
        .list-item {
            padding: 24rpx 20rpx;
            background: #ffffff;
            border-radius: 12rpx;
            margin-top: 24rpx;
            &:first-child {
                margin-top: 0;
            }
            .left {
                .user-avatar {
                    position: relative;
                    .user-status {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        background: #fd0100;
                        border-radius: 16rpx 0rpx 16rpx 0rpx;
                        padding: 6rpx 12rpx;
                    }
                }
                .status {
                    padding: 4rpx 10rpx;
                    background: #fd0100;
                    border-radius: 12rpx 0rpx 12rpx 0rpx;
                }
            }
        }
    }
}
</style>
