<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>签署结果</title>
    <script type="text/javascript" src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .result {
            text-align: center;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .img {
            width: 120px;
            height: 120px;
        }

        .title {
            margin-bottom: 150px;
            font-weight: bold;
            font-size: 32px;
            color: #f60;
            line-height: 25px;
        }

        .btn {
            width: 271px;
            height: 48px;
            background: #f60;
            border-radius: 24px;
            font-weight: 400;
            font-size: 18px;
            color: #ffffff;
            line-height: 48px;
            text-align: center;
            margin: 0 auto;
            cursor: pointer;
            position: fixed;
            bottom: 100px;
        }
    </style>
</head>

<body>
    <div class="result">
        <!-- <div class="title">人脸认证成功</div> -->
        <div class="btn" id="completeBtn">返回</div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const urlParams = new URLSearchParams(window.location.search);
            const biztoken = urlParams.get('BizToken');
            const completeBtn = document.getElementById('completeBtn');
            completeBtn.addEventListener('click', function () {
                uni.postMessage({
                    data: {
                        action: 'faceRecognitionSuccess',
                        biztoken: biztoken
                    }
                });
            });
        });
    </script>
</body>

</html>