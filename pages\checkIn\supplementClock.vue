<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="补卡" isPerch isBack :showRight="false">
                <view slot="right">
                    <text class="mf-font-28 mf-weight-bold" style="color: #999">补卡记录</text>
                </view>
            </c-navBar>
        </section>
        <section class="content">
            <view class="checkin-time flex align-center justify-between" @click="show = true">
                <text class="mf-font-28 mf-weight-bold" style="color: #1a1a1a">领补助时间</text>
                <view class="flex align-center gap-10">
                    <text class="mf-font-28 mf-weight-bold" style="color: #999999">{{
                        $u.timeFormat(chooseTime, "yyyy-mm-dd hh:MM")
                    }}</text>
                    <u-icon name="arrow-right" color="#999999" size="16"></u-icon>
                </view>
            </view>
            <view class="checkin-info">
                <text class="mf-font-28 mf-weight-bold" style="color: #1a1a1a">补卡原因</text>
                <view style="margin-top: 20rpx">
                    <u--textarea
                        v-model="data.record"
                        placeholderStyle="color: #999"
                        count
                        border="none"
                        height="300rpx"
                        maxlength="80"
                        placeholder="请备注外勤领补助原因"
                    ></u--textarea>
                </view>
            </view>
        </section>
        <view class="line" v-if="false"></view>
        <section class="process" v-if="false">
            <text class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">审批流程</text>
            <view class="process-list flex align-center justify-between">
                <view class="process-item flex flex-col align-center gap-10">
                    <view class="avatar">
                        <image class="avatar-img" src="/static/mine/avatar.png" mode="scaleToFill" />
                        <view class="avatar-text mf-font-20" style="color: #fff">队员</view>
                    </view>
                    <text class="mf-font-28 mf-weight-bold" style="color: #1a1a1a">张三</text>
                </view>
                <view class="process-item flex flex-col align-center gap-10">
                    <view class="avatar">
                        <image class="avatar-img" src="/static/mine/avatar.png" mode="scaleToFill" />
                        <view class="avatar-text mf-font-20" style="color: #fff">队员</view>
                    </view>
                    <text class="mf-font-28 mf-weight-bold" style="color: #1a1a1a">张三</text>
                </view>
                <view class="process-item flex flex-col align-center gap-10">
                    <view class="avatar">
                        <image class="avatar-img" src="/static/mine/avatar.png" mode="scaleToFill" />
                        <view class="avatar-text mf-font-20" style="color: #fff">队员</view>
                    </view>
                    <text class="mf-font-28 mf-weight-bold" style="color: #1a1a1a">张三</text>
                </view>
            </view>
            <view class="steps">
                <view class="flex align-center justify-between">
                    <view class="step-item flex justify-center">
                        <view class="step-item-text flex flex-center mf-font-20"> 1 </view>
                    </view>
                    <view class="step-item flex justify-center">
                        <view class="step-item-text flex flex-center mf-font-20"> 2 </view>
                    </view>
                    <view class="step-item flex justify-center">
                        <view class="step-item-text flex flex-center mf-font-20"> 3 </view>
                    </view>
                </view>
                <view class="step-line"></view>
            </view>
        </section>
        <section class="process-btn">
            <view class="btn flex flex-center" @click="handleReClock">
                <text class="mf-font-32 mf-weight-bold" style="color: #fff">提交</text>
            </view>
        </section>
        <u-datetime-picker
            :show="show"
            v-model="chooseTime"
            mode="datetime"
            :max-date="maxDate"
            @close="show = false"
            @cancel="show = false"
            close-on-click-overlay
            @confirm="confirm"
        ></u-datetime-picker>
    </view>
</template>

<script>
import { startFaceVerify } from "../../common/js/cloudIai.js";
export default {
    data() {
        return {
            remark: "",
            show: false,
            chooseTime: new Date().getTime(),
            maxDate: new Date().getTime(),
            data: {
                lat: "",
                lng: "",
                province: "",
                city: "",
                poi_address: "",
                date: "",
                record: "",
            },
            userInfo: {},
        };
    },
    onLoad(options) {
        this.data = JSON.parse(options.data);
        this.userInfo = uni.getStorageSync("userInfo");
        console.log(this.data);
    },
    onShow() {
        if (uni.getStorageSync("faceRecognitionSuccess")) {
            const isFace = uni.getStorageSync("faceRecognitionSuccess");
            uni.removeStorageSync("faceRecognitionSuccess");
            if (isFace === "ok") {
                this.submitReClock();
            } else {
                this.$fn.showToast("人脸认证失败,请重试");
            }
        }
    },
    methods: {
        confirm(e) {
            console.log(e);
            this.chooseTime = e.value;
            this.show = false;
        },
        async submitReClock() {
            try {
                this.$fn.showToast("正在提交补卡申请...");
                const res = await this.$api.reClock({
                    ...this.data,
                    date: this.$u.timeFormat(this.chooseTime, "yyyy-mm-dd hh:MM"),
                });
                if (res.code === 200) {
                    this.$fn.showToast("补领补助成功");
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1000);
                } else {
                    this.$fn.showToast(res.msg || "补领补助失败");
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("补领补助失败");
            }
        },
        // 补领补助
        async handleReClock() {
            try {
                if (!this.data.record) {
                    this.$fn.showToast("请填写补卡原因");
                    return;
                }
                if (this.userInfo.face_detect === 1) {
                    const ruleId = "1";
                    this.$fn.showToast("正在启动人脸核身...");
                    await startFaceVerify(ruleId);
                } else {
                    await this.submitReClock();
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("补领补助失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        padding: 24rpx;
        .checkin-time {
            padding: 24rpx 0 40rpx;
            border-bottom: 2rpx solid #e6e9f0;
        }
        .checkin-info {
            margin-top: 24rpx;
            ::v-deep .u-textarea {
                background-color: #f7f8fa;
            }
            ::v-deep .u-textarea__count {
                background-color: #f7f8fa !important;
            }
        }
    }
    .line {
        width: 100%;
        height: 10rpx;
        background: #f2f4f7;
    }
    .process {
        padding: 32rpx 24rpx;
        position: relative;
        .process-list {
            margin-top: 20rpx;
            .process-item {
                .avatar {
                    width: 128rpx;
                    height: 128rpx;
                    border-radius: 16rpx;
                    position: relative;
                    .avatar-img {
                        width: 128rpx;
                        height: 128rpx;
                    }
                    .avatar-text {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        text-align: center;
                        background: #ebb84c;
                        border-radius: 0 0 16rpx 16rpx;
                    }
                }
            }
        }
        .steps {
            margin-top: 20rpx;
            z-index: 10;
            position: relative;
            .step-item {
                width: 128rpx;
                margin-right: 3rpx; // 微调位置
                .step-item-text {
                    width: 35rpx;
                    height: 35rpx;
                    border-radius: 50%;
                    background: #fd0100;
                    text-align: center;
                    color: #fff;
                }
            }
            .step-line {
                position: absolute;
                bottom: 17rpx;
                left: 0;
                right: 0;
                width: 100%;
                height: 2rpx;
                z-index: -1;
                border-bottom: 2rpx dashed #00000060;
            }
        }
    }
    .process-btn {
        margin-top: 128rpx;
        padding: 0 24rpx;
        .btn {
            width: 100%;
            height: 96rpx;
            background: #fd0100;
            border-radius: 12rpx;
        }
    }
}
</style>
