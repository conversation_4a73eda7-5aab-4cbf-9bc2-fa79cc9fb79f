<template>
    <view class="view">
        <!-- <section class="bg">
            <image class="bg-img" src="/static/common/office-bg.png" mode="scaleToFill" />
        </section> -->
        <section class="navbar">
            <c-navBar isPerch title="资金绩效数据统计" :isBack="true" backgroundColor="#FD0200" color="#fff"> </c-navBar>
        </section>
        <section class="filter flex align-center justify-between gap-14">
            <view class="filter-item flex-1 flex align-center justify-between" @click="showDate = true">
                <text class="mf-font-24" style="color: #999999">日期</text>
                <u-input
                    :value="$u.timeFormat(form.date, 'yyyy-mm')"
                    disabled
                    fontSize="24rpx"
                    color="#333"
                    disabledColor="transparent"
                    inputAlign="right"
                    suffixIcon="arrow-right"
                    border="none"
                    :suffixIconStyle="{ fontSize: '24rpx' }"
                    placeholder="请选择日期"
                />
            </view>
            <view class="filter-item flex-1 flex align-center justify-between">
                <text class="mf-font-24" style="color: #999999">占比</text>
                <u-input
                    v-model="form.rate"
                    color="#333"
                    fontSize="24rpx"
                    inputAlign="right"
                    border="none"
                    placeholder="请输入占比"
                    suffixIcon="%"
                    :suffixIconStyle="{ fontSize: '24rpx' }"
                    @change="handleChangeRate"
                />
            </view>
        </section>

        <section class="cards">
            <scroll-view scroll-y style="height: calc(100vh - 380rpx)">
                <view class="card" v-for="(item, key) in list" :key="key">
                    <view class="flex align-center justify-between" @click="handleToFundsStatisticsChildren(item.level)">
                        <view class="flex flex-col gap-16">
                            <text class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ item.title }}</text>
                            <text class="mf-font-28" style="color: #999999">{{ `${item.used_total}/${item.total}` }}</text>
                        </view>
                        <u-icon name="arrow-right" size="32rpx"></u-icon>
                    </view>
                    <view class="circle-progress-wrapper">
                        <c-circleProgress
                            :percentage="item.used_rate"
                            :size="230"
                            :stroke-width="15"
                            stroke-color="#EBB84C"
                            background-color="#FFF2ED"
                            text-color="#1A1A1A"
                            sub-text-color="#999999"
                        ></c-circleProgress>
                    </view>
                    <view class="progres-item flex flex-col gap-16" v-for="(children, index) in item.child" :key="index">
                        <view class="mf-font-24 mf-weight-bold" style="color: #333333">{{ children.type_name }}</view>
                        <view class="flex align-center justify-between">
                            <text class="mf-font-20" style="color: #666666">{{ `${children.used_total}/${children.total}` }}</text>
                            <text class="mf-font-20" style="color: #333333">{{ children.used_rate_tag }}</text>
                        </view>
                        <view class="flex-1">
                            <u-line-progress
                                :percentage="children.used_rate"
                                height="14rpx"
                                :showText="false"
                                inactive-color="#FFF5F5"
                                active-color="#FD0100"
                            ></u-line-progress>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </section>
        <u-datetime-picker
            :show="showDate"
            :value="form.date"
            close-on-click-overlay
            @confirm="handleConfirmDate"
            @close="showDate = false"
            @cancel="showDate = false"
            mode="year-month"
        ></u-datetime-picker>
    </view>
</template>

<script>
export default {
    data() {
        return {
            proportion: 0,
            showDate: false,
            modelVale: 60,
            target: 100,
            form: {
                date: "", // 日期
                only_not_compliance: "", // 只看未达标(这里不作用)
                level: 0, // 层级
                town_id: "", // 镇级id(这里不作用)
                rate: 30, // 占比
                page: 1,
                page_size: 10,
                parent_type: 1,
                type: 4,
            },
            list: [], // 资金绩效列表
        };
    },
    computed: {
        progressPercentage() {
            // 计算进度百分比，默认为85%
            return this.proportion ? Number(this.proportion) : 85;
        },
    },
    onLoad(options) {
        this.form.parent_type = options.parent_type;
        this.form.date = this.$u.timeFormat(new Date().getTime(), "yyyy-mm");
        this.getFundsStatistics();
    },
    methods: {
        // 选择占比
        handleChangeRate() {
            this.form.page = 1;
            uni.$u.throttle(this.getFundsStatistics, 500, false);
        },
        // 选择日期
        handleConfirmDate(e) {
            this.form.page = 1;
            this.form.date = this.$u.timeFormat(e.value, "yyyy-mm");
            this.showDate = false;
            this.getFundsStatistics();
        },
        // 获取资金绩效统计
        async getFundsStatistics() {
            try {
                const res = await this.$api.fundsStatistics(this.form);
                if (res.code === 200) {
                    this.list = res.data;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取资金绩效时发生错误");
            }
        },
        // 下级资金绩效统计
        handleToFundsStatisticsChildren(level) {
            this.$fn.jumpPage(`/pages/office/dataStatisticsChildren?level=${level}&parent_type=${this.form.parent_type}`, true);
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fbfbfb;
    min-height: 100vh;
    .filter {
        padding: 16rpx 24rpx;
        background: #fff5f5;
        .filter-item {
            border: 1rpx solid #999999;
            border-radius: 8rpx;
            padding: 12rpx 18rpx;
        }
    }
    .cards {
        padding: 32rpx 24rpx;
        .card {
            background: #fff;
            border-radius: 16rpx;
            padding: 24rpx;
            box-shadow: 0rpx 12rpx 12rpx -4rpx rgba(224, 224, 224, 0.28);
            margin-top: 24rpx;
            &:first-child {
                margin-top: 0;
            }
            .circle-progress-wrapper {
                margin: 40rpx auto;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .progres-item {
                margin-top: 32rpx;
                &:first-child {
                    margin-top: 0;
                }
            }
        }
    }
}
</style>
