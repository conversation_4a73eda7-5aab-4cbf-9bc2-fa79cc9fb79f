<template>
    <view class="view">
        <section class="bg"></section>
        <section class="content">
            <view class="user-info">
                <u-image src="/static/mine/avatar.png" width="128rpx" height="128rpx" radius="16rpx" mode="scaleToFill "></u-image>
                <view class="user-name mf-font-36" style="color: #fff">阿米拉多</view>
                <view class="flex flex-col flex-center gap-12 mf-font-20" style="color: #fff; margin-top: 16rpx">
                    <text>正在通话中</text>
                    <text>10:10:10</text>
                </view>
                <view class="user-status mf-font-20" style="color: #fff">邀请您视频通话...</view>
            </view>
            <view class="actions" v-if="status == 1">
                <view class="content flex align-center justify-between">
                    <view class="action flex flex-center">
                        <u-image src="/static/common/hand-up.png" width="96rpx" height="96rpx" mode="aspectFill"></u-image>
                    </view>
                    <view class="action flex flex-center">
                        <u-image src="/static/common/answer.png" width="96rpx" height="96rpx" mode="aspectFill"></u-image>
                    </view>
                </view>
            </view>
            <view class="actions2" v-if="status == 2">
                <view class="content flex align-center justify-between">
                    <view class="action flex flex-center" @click="isMic = !isMic">
                        <u-image src="/static/common/mic1.png" width="96rpx" height="96rpx" mode="aspectFill" v-if="isMic"></u-image>
                        <u-image src="/static/common/mic2.png" width="96rpx" height="96rpx" mode="aspectFill" v-else></u-image>
                    </view>
                    <view class="action flex flex-center">
                        <u-image src="/static/common/hand-up.png" width="96rpx" height="96rpx" mode="aspectFill"></u-image>
                    </view>
                    <view class="action flex flex-center" @click="isSpeaker = !isSpeaker">
                        <u-image
                            src="/static/common/speaker1.png"
                            width="96rpx"
                            height="96rpx"
                            mode="aspectFill"
                            v-if="isSpeaker"
                        ></u-image>
                        <u-image src="/static/common/speaker2.png" width="96rpx" height="96rpx" mode="aspectFill" v-else></u-image>
                    </view>
                </view>
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            status: 2,
            isMic: true,
            isSpeaker: true,
        };
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
.view {
    height: 100vh;

    .bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #000;
        backdrop-filter: blur(179rpx);
        opacity: 0.6;
        z-index: -1;
    }
    .content {
        height: 100%;
        position: relative;
        // gap: 114rpx;
        .user-info {
            position: absolute;
            top: 30%;
            left: 50%;
            transform: translate(-50%, -50%);
            .user-name {
                margin-top: 48rpx;
                text-align: center;
            }
            .user-status {
                margin-top: 16rpx;
                text-align: center;
            }
        }
        .actions {
            position: absolute;
            bottom: calc(150rpx + env(safe-area-inset-bottom));
            // width: auto;
            width: 100%;
            .content {
                padding: 0 58rpx;
                .action {
                    width: 160rpx;
                    height: 160rpx;
                    border-radius: 32rpx;
                    margin: 0 110rpx;

                    &:nth-child(1) {
                        background: #fd0100;
                    }
                    &:nth-child(2) {
                        background: #30a84b;
                    }
                }
            }
        }

        .actions2 {
            position: absolute;
            bottom: calc(150rpx + env(safe-area-inset-bottom));
            // width: auto;
            width: 100%;
            .content {
                padding: 0 58rpx;
                .action {
                    width: 128rpx;
                    height: 128rpx;
                    border-radius: 32rpx;
                    &:nth-child(1) {
                        background: #a3a3a3;
                    }
                    &:nth-child(2) {
                        background: #fd0100;
                    }
                    &:nth-child(3) {
                        background: #a3a3a3;
                    }
                }
            }
        }
    }
}
</style>
