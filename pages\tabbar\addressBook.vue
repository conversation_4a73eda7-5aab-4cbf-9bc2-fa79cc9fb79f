<template>
    <view class="view">
        <section class="home-bg">
            <image src="/static/common/home-bg.png" mode="scaleToFill" />
        </section>
        <section class="navbar">
            <c-navBar isTran isPerch title="通讯录" :isBack="false" color="#fff"> </c-navBar>
        </section>
        <section class="search" @click="$fn.jumpPage('/pages/addressBook/search', true)">
            <view class="search-box">
                <view class="flex flex-center gap-12" style="width: 100%">
                    <text style="color: #c0c4cc">搜索</text>
                </view>
            </view>
        </section>
        <section class="tabs">
            <u-tabs
                :list="tabs"
                :current="currentTab"
                activeStyle="color: #FD0100; font-weight: bold;"
                lineColor="#FD0100"
                @click="handleClick"></u-tabs>
        </section>
        <section>
            <messageList ref="messageList" v-show="currentTab === 0" />
            <organizationalStructure ref="organizationalStructure" v-show="currentTab === 1" @frameworkDataNum="handleFrameworkDataNum" />
            <askForLeave ref="askForLeave" v-show="currentTab === 2" @askForLeaveDataNum="handleAskForLeaveDataNum" />
            <reporter ref="reporter" v-show="currentTab === 3" @reporterDataNum="handleReporterDataNum" />
        </section>
    </view>
</template>

<script>
import messageList from "../addressBook/messageList.vue";
import organizationalStructure from "../addressBook/organizationalStructure.vue";
import askForLeave from "../addressBook/askForLeave.vue";
import reporter from "../addressBook/reporter.vue";

export default {
    components: {
        messageList,
        reporter,
        organizationalStructure,
        askForLeave,
    },
    data() {
        return {
            currentTab: 1,
            tabs: [
                {
                    name: "消息",
                },
                {
                    name: "组织架构(0)",
                },
                {
                    name: "请假人(0)",
                },
                {
                    name: "报备人(0)",
                },
            ],
        };
    },
    onLoad() {
        this.$nextTick(() => {
            if (this.$refs.organizationalStructure) {
                this.$refs.organizationalStructure.handleGetFrameworkList();
            }
            if (this.$refs.askForLeave) {
                this.$refs.askForLeave.handleGetLeaveList();
            }
            if (this.$refs.reporter) {
                this.$refs.reporter.handleGetReportUserList();
            }
        });
    },
    onPullDownRefresh() {
        this.$nextTick(() => {
            if (this.$refs.organizationalStructure) {
                this.$refs.organizationalStructure.handleGetFrameworkList();
            }
            if (this.$refs.askForLeave) {
                this.$refs.askForLeave.handleGetLeaveList();
            }
            if (this.$refs.reporter) {
                this.$refs.reporter.handleGetReportUserList();
            }
        });
    },
    methods: {
        handleFrameworkDataNum(num) {
            this.tabs[1].name = `组织架构(${num})`;
        },
        handleAskForLeaveDataNum(num) {
            this.tabs[2].name = `请假人(${num})`;
        },
        handleReporterDataNum(num) {
            this.tabs[3].name = `报备人(${num})`;
        },
        handleClick(tab) {
            console.log(tab);
            if (tab.index == this.currentTab) return;
            this.currentTab = tab.index;

            switch (tab.index) {
                case 0:
                    // #ifndef APP-PLUS
                    this.$fn.showToast("请前往APP使用");
                    return;
                    // #endif
                    // 打开会话列表
                    this.$refs.messageList.getList();
                    break;
                case 1:
                    this.$nextTick(() => {
                        this.$refs.organizationalStructure.handleGetFrameworkList();
                    });
                    break;
                case 2:
                    this.$nextTick(() => {
                        this.$refs.askForLeave.handleGetLeaveList();
                    });
                    break;
                case 3:
                    this.$nextTick(() => {
                        this.$refs.reporter.handleGetReportUserList();
                    });
                    break;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    position: relative;
    // min-height: 100vh;

    .home-bg {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: 580rpx;
        z-index: -999;

        image {
            width: 100%;
            height: 100%;
        }
    }

    .search {
        padding: 24rpx;

        .search-box {
            padding: 20rpx 0;
            border-radius: 12rpx;
            background: #ffffff40;
        }
    }

    .tabs {
        background: #fff5f5;
        padding: 10rpx 0;
    }
}
</style>
