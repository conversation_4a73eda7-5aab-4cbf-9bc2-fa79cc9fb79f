<template>
    <view class="view">
        <section class="bg">
            <image class="bg-img" src="/static/common/office-bg.png" mode="scaleToFill" />
        </section>
        <section class="navbar">
            <c-navBar isTran isPerch title="资金绩效" :isBack="true" color="#fff"> </c-navBar>
        </section>
        <section class="user-info" style="pointer-events: none;">
            <image src="/static/common/user-card.png" mode="widthFix" style="pointer-events: auto;" />
            <view class="user-info-item" style="pointer-events: auto;">
                <view class="flex align-center gap-24">
                    <view class="user-avatar">
                        <u-image
                            :src="preApplyFunds.avatar || '/static/mine/avatar.png'"
                            width="128rpx"
                            height="128rpx"
                            radius="16rpx"
                            mode="scaleToFill "
                            style="pointer-events: auto;"
                        ></u-image>
                        <view class="user-status mf-font-20" style="color: #fff; pointer-events: auto;">
                            {{ preApplyFunds.online_status ? preApplyFunds.online_status.name : "获取失败" }}
                        </view>
                    </view>
                    <view class="flex flex-col gap-30">
                        <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a; pointer-events: auto;">{{ preApplyFunds.nickname }}</view>
                        <view class="mf-font-28" style="color: #ebb84c; pointer-events: auto;">{{ preApplyFunds.role_name }}</view>
                    </view>
                </view>
            </view>
        </section>
        <section class="content flex flex-col gap-24">
            <view class="content-item flex align-center gap-24" @click="handleJumpBenefitView">
                <u-image src="/static/common/performance1.png" width="104rpx" height="104rpx" shape="circle" mode="aspectFill"></u-image>
                <view class="content-item-info flex-1 flex align-center justify-between gap-16">
                    <view class="flex flex-col gap-12 flex-1">
                        <view class="flex align-center gap-12">
                            <text class="mf-font-32-h-32 mf-weight-bold">强基惠民经费</text>
                            <u-tag
                                text="填报"
                                plain
                                size="mini"
                                type="error"
                                @click.stop="handleJumpBenefit"
                            ></u-tag>
                        </view>
                        <view class="progress-wrapper flex flex-col gap-12">
                            <view class="progress-percentage mf-font-28">{{ preApplyFunds.benefit_rate }}%</view>
                            <view class="progress-bar">
                                <view class="progress-bar-inner" :style="{ width: preApplyFunds.benefit_rate + '%' }"></view>
                            </view>
                        </view>
                    </view>
                    <u-icon name="arrow-right" size="32rpx"></u-icon>
                </view>
            </view>
            <view v-if="false" class="content-item flex align-center gap-24" @click="handleJumpRelateView">
                <u-image src="/static/common/performance2.png" width="104rpx" height="104rpx" shape="circle" mode="aspectFill"></u-image>
                <view class="content-item-info flex-1 flex align-center justify-between gap-16">
                    <view class="flex flex-col gap-12 flex-1">
                        <view class="flex align-center gap-12">
                            <text class="mf-font-32-h-32 mf-weight-bold">有关经费</text>
                            <u-tag
                                text="填报"
                                plain
                                size="mini"
                                type="error"
                                @click.stop="handleJumpRelate"
                            ></u-tag>
                        </view>
                        <view class="progress-wrapper flex flex-col gap-12">
                            <view class="progress-percentage mf-font-28">{{ preApplyFunds.relate_rate }}%</view>
                            <view class="progress-bar">
                                <view class="progress-bar-inner" :style="{ width: preApplyFunds.relate_rate + '%' }"></view>
                            </view>
                        </view>
                    </view>
                    <u-icon name="arrow-right" size="32rpx"></u-icon>
                </view>
            </view>
        </section>
        <!-- <section class="footer">
            <view class="btn flex flex-center mf-font-36 mf-weight-bold" style="color: #fff">数据统计</view>
        </section> -->
    </view>
</template>

<script>
export default {
    data() {
        return {
            preApplyFunds: {}, // 资金绩效
        };
    },
    onShow() {
        this.handleGetPreApplyFunds();
    },
    methods: {
        // 前往强基惠民查看
        handleJumpBenefitView() {
            if (this.preApplyFunds.benefit_view_auth == 0) {
                this.$fn.showToast("你没有权限查看");
                return;
            }
            this.$fn.jumpPage("/pages/office/dataStatistics?parent_type=1", true);
        },
        // 前往有关经费查看
        handleJumpRelateView() {
            if (this.preApplyFunds.relate_view_auth == 0) {
                this.$fn.showToast("你没有权限查看");
                return;
            }
            this.$fn.jumpPage("/pages/office/dataStatistics?parent_type=2", true);
        },
        // 前往强基惠民填报
        handleJumpBenefit() {
            console.log("前往强基惠民填报");
            if (this.preApplyFunds.benefit_apply_auth === 0) {
                this.$fn.showToast("你没有权限填报");
                return;
            }
            this.$fn.jumpPage("/pages/office/financialPerformanceDetail?parent_type=1", true);
        },
        // 前往有关经费填报
        handleJumpRelate() {
            console.log("前往有关经费填报");
            if (this.preApplyFunds.relate_apply_auth === 0) {
                this.$fn.showToast("你没有权限填报");
                return;
            }
            this.$fn.jumpPage("/pages/office/financialPerformanceDetail?parent_type=2", true);
        },
        // 获取资金绩效
        async handleGetPreApplyFunds() {
            try {
                const res = await this.$api.preApplyFunds();
                if (res.code === 200) {
                    this.preApplyFunds = res.data;
                }
            } catch (error) {
                console.log(error);
                this.$fn.showToast("获取资金绩效失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fbfbfb;
    min-height: 100vh;

    .bg {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        .bg-img {
            width: 100%;
            height: 600rpx;
        }
    }
    .user-info {
        margin: 24rpx 24rpx 0 24rpx;
        position: relative;
        z-index: 1;
        overflow: hidden;
        height: 192rpx;
        border-radius: 16rpx;
        box-shadow: 0rpx 12rpx 12rpx -4rpx rgba(224, 224, 224, 0.28);
        image {
            width: 100%;
            height: 192rpx;
            position: relative;
            z-index: -1;
        }

        .user-info-item {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 32rpx;
            .user-avatar {
                position: relative;
                .user-status {
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    background: #fd0100;
                    border-radius: 16rpx 0rpx 16rpx 0rpx;
                    padding: 4rpx 8rpx;
                }
            }
        }
    }
    .content {
        padding: 32rpx 24rpx;
        position: relative;
        z-index: 999;
        .content-item {
            background: #ffffff;
            border-radius: 16rpx;
            padding: 34rpx 24rpx;
            box-shadow: 0rpx 6rpx 21rpx 0rpx rgba(0, 0, 0, 0.05);

            .content-item-info {
                .progress-wrapper {
                    .progress-percentage {
                        color: #999999;
                    }

                    .progress-bar {
                        height: 10rpx;
                        background-color: #f5f5f5;
                        border-radius: 4rpx;
                        overflow: hidden;

                        .progress-bar-inner {
                            height: 100%;
                            background-color: #fd0100;
                            border-radius: 4rpx;
                        }
                    }
                }
            }
        }
    }
    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: #fbfbfb;
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
        .btn {
            width: 100%;
            height: 100%;
            background: #fd0100;
            border-radius: 12rpx;
        }
    }
}
</style>
