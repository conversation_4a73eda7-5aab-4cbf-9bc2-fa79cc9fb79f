<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="调度" :isBack="true" color="#000"> </c-navBar>
        </section>
        <section class="search">
            <view class="search-box">
                <u-input placeholder="搜索" border="none" input-align="center" v-model="keywords" @blur="handleDebounceSearchUser" @confirm="handleDebounceSearchUser" />
            </view>
        </section>
        <section class="groups">
            <scroll-view v-if="searchList.length === 0" scroll-y class="scroll-view" style="height: calc(100vh - 390rpx)">
                <view class="group-item" v-for="(item, index) in list" :key="index">
                    <!-- 分组头部 -->
                    <view class="group-header flex align-center justify-between" @click="toggleGroup(index)">
                        <view class="flex align-center gap-20">
                            <u-image src="/static/common/group-icon2.png" width="72rpx" height="72rpx" radius="8rpx" mode="aspectFill"></u-image>
                            <text class="mf-font-32 mf-weight-bold">{{ item.group_name }}</text>
                        </view>
                        <u-icon :name="item.open ? 'arrow-down' : 'arrow-right'" size="32rpx"></u-icon>
                    </view>
                    <!-- 展开内容 -->
                    <view v-if="item.open" class="expand">
                        <!-- 有user直接渲染用户 -->
                        <view v-if="item.user" class="users flex flex-col gap-16" style="margin-left: 16rpx">
                            <view class="user flex-1 flex align-center justify-between" v-for="user in item.user" :key="user.id" @click="handleCheck(user)">
                                <view class="flex align-center gap-24">
                                    <view class="user-avatar">
                                        <u-image :src="user.avatar || '/static/common/group-icon2.png'" width="100rpx" height="100rpx" radius="16rpx" mode="scaleToFill "></u-image>
                                        <view class="user-status mf-font-20" style="color: #fff">{{ user.online_status.name }}</view>
                                    </view>
                                    <view class="flex flex-col gap-30">
                                        <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ user.nickname }}</view>
                                        <view class="mf-font-28" style="color: #ebb84c">{{ user.role_name || "驻村队长" }}</view>
                                    </view>
                                </view>
                                <view class="checkbox" v-if="user.id !== userInfo.id">
                                    <view class="uncheck-box" v-if="!isChecked(user)"></view>
                                    <view class="check-box" v-else></view>
                                </view>
                                <!-- <view class="action-box">
                                    <view
                                        class="btn mf-font-28"
                                        style="color: #1a1a1a"
                                        @click.stop="$fn.jumpPage('/pages/addressBook/message')"
                                    >
                                        发私信
                                    </view>
                                </view> -->
                            </view>
                        </view>
                        <!-- 有list则渲染下一级分组 -->
                        <view v-if="item.list" class="list flex flex-col gap-16" style="margin-left: 16rpx">
                            <view class="sub-group" v-for="(sub, subIdx) in item.list" :key="subIdx">
                                <view class="sub-group-header flex align-center justify-between" @click="toggleSubGroup(index, subIdx)">
                                    <view class="flex align-center gap-20">
                                        <u-image src="/static/common/group-icon2.png" width="72rpx" height="72rpx" radius="8rpx" mode="aspectFill"></u-image>
                                        <text class="mf-font-32 mf-weight-bold">{{ sub.group_name }}</text>
                                    </view>
                                    <u-icon :name="sub.open ? 'arrow-down' : 'arrow-right'" size="28rpx"></u-icon>
                                </view>
                                <view v-if="sub.open" class="flex flex-col gap-16" style="margin-left: 16rpx">
                                    <view class="user flex-1 flex align-center justify-between" v-for="user in sub.user" :key="user.id" @click="handleCheck(user)">
                                        <view class="flex align-center gap-24">
                                            <view class="user-avatar">
                                                <u-image :src="user.avatar || '/static/common/group-icon2.png'" width="100rpx" height="100rpx" radius="16rpx" mode="scaleToFill "></u-image>
                                                <view class="user-status mf-font-20" style="color: #fff">
                                                    {{ user.online_status.name }}
                                                </view>
                                            </view>
                                            <view class="flex flex-col gap-30">
                                                <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ user.nickname }}</view>
                                                <view class="mf-font-28" style="color: #ebb84c">{{ user.role_name || "驻村队长" }}</view>
                                            </view>
                                        </view>
                                        <view class="checkbox" v-if="user.id !== userInfo.id">
                                            <view class="uncheck-box" v-if="!isChecked(user)"></view>
                                            <view class="check-box" v-else></view>
                                        </view>
                                        <!-- <view class="action-box">
                                            <view
                                                class="btn mf-font-28"
                                                style="color: #1a1a1a"
                                                @click.stop="$fn.jumpPage('/pages/addressBook/message')"
                                            >
                                                发私信
                                            </view>
                                        </view> -->
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </scroll-view>
            <scroll-view v-else scroll-y class="scroll-view" style="height: calc(100vh - 390rpx)">
                <view class="user-item flex align-center justify-between" v-for="item in searchList" :key="item.id" @click="handleCheck(item)">
                    <view class="flex align-center gap-24">
                        <view class="user-avatar flex align-center gap-24">
                            <u-image :src="item.avatar || '/static/common/group-icon2.png'" width="100rpx" height="100rpx" radius="16rpx" mode="scaleToFill "></u-image>
                        </view>
                        <view class="user-info flex flex-col gap-30">
                            <view class="user-name mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ item.nickname }}</view>
                            <view class="user-role mf-font-28" style="color: #ebb84c">
                                {{ item.role ? item.role.role_name : "获取失败" }}
                                {{ item.id }}{{ userInfo.id }}
                            </view>
                        </view>
                    </view>
                    <view class="checkbox" v-if="item.id !== userInfo.id">
                        <view class="uncheck-box" v-if="!isChecked(item)"></view>
                        <view class="check-box" v-else></view>
                    </view>
                </view>
            </scroll-view>
        </section>
        <section class="footer">
            <view class="btn flex flex-center mf-font-36 mf-weight-bold" :style="checkedUser
                    ? 'color: #fff; background: #fd0100;'
                    : 'color: #fff; background: #fd0100; opacity: 0.5; pointer-events: none;'
                " @click="handleCall">
                发起调度
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            keywords: "",
            frameworkData: {},
            list: [], // 组织架构列表
            searchList: [], // 搜索列表
            checkedUser: null,
            userInfo: {},
        };
    },
    computed: {
        hasUser() {
            // 判断当前list中是否存在用户
            if (Array.isArray(this.list) && this.list.length > 0) {
                // 检查每个分组下是否有user数组且长度大于0
                return this.list.some((group) => Array.isArray(group.user) && group.user.length > 0);
            }
            return false;
        },
    },
    onShow() { },
    onLoad(options) {
        this.userInfo = uni.getStorageSync("userInfo");
        this.handleGetFrameworkList();
    },
    methods: {
        // 防抖搜索
        handleDebounceSearchUser() {
            if (this.keywords.length > 0) {
                this.$u.debounce(this.handleSearch, 500);
            } else {
                this.searchList = [];
                this.handleGetFrameworkList();
            }
        },
        // 搜索
        async handleSearch() {
            try {
                const res = await this.$api.searchUser({ keywords: this.keywords });
                if (res.code === 200) {
                    console.log(res.data);
                    this.searchList = res.data.list;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("搜索失败");
            }
        },
        // 拨打音视频
        handleCall() {
            try {
                // #ifndef APP-PLUS
                this.$fn.showToast("请在APP中发起调度");
                return;
                // #endif
                if (!this.checkedUser) {
                    this.$fn.showToast("请选择调度人员");
                    return;
                }

                const options = {
                    userIDList: [String(this.checkedUser.id)],
                    callMediaType: 2, // 1 -- audio call，2 -- video call
                    callParams: { roomID: 0, strRoomID: `mlRoomId${this.checkedUser.id}`, timeout: 30 },
                    OfflinePushInfo: {
                        title: `${this.userInfo.nickname}发起调度`,
                        description: `点击查看`,
                    },
                };
                console.log("checkedUser:", this.checkedUser);
                console.log("userIdList:", [String(this.checkedUser.id)]);
                console.log("userID (self):", uni.getStorageSync("userInfo").id);
                //【3】start 1v1 video call
                uni.$TUICallKit.calls(options, (res) => {
                    if (res.code === 0) {
                        console.log("【通话发起成功】:", res);
                        this.$fn.showToast("通话发起成功");
                    } else {
                        console.error("【通话发起失败】:", res);
                        uni.showModal({
                            title: "通话发起失败",
                            content: "可能是对方未上线或未登陆过该系统",
                            showCancel: false,
                            confirmText: "确定",
                        });
                    }
                });
            } catch (error) {
                console.error("【通话发起失败】: ", error);
                this.$fn.showToast("发起调度时出现错误");
            }
        },
        // 检查是否选中
        isChecked(item) {
            return this.checkedUser && this.checkedUser.id === item.id;
        },

        // 获取组织架构列表
        async handleGetFrameworkList() {
            try {
                const res = await this.$api.getFrameworkList();
                if (res.code === 200) {
                    this.frameworkData = res.data;
                    this.$emit("frameworkDataNum", res.data.village_total_user);
                    this.list = await this.dataCollation(res.data);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取组织架构失败");
            }
        },

        // 数据整理
        async dataCollation(data) {
            let netData = [];
            // 遍历所有数据
            for (const [key, value] of Object.entries(data)) {
                // 处理市级和派驻组数据
                if (typeof value === "object" && !Array.isArray(value) && value !== null) {
                    if (value.user?.length > 0) {
                        netData.push({
                            group_name: value.framework_label,
                            user: value.user,
                            open: false,
                        });
                    }
                }

                // 处理乡镇数据（乡镇下有村，村下才是用户）
                if (Array.isArray(value)) {
                    value.forEach((ele) => {
                        netData.push({
                            group_name: ele.town_name,
                            list: ele.role_group.map((e) => {
                                return {
                                    group_name: e.framework_label,
                                    user: e.user,
                                    open: false,
                                };
                            }),
                            open: false,
                        });
                    });
                }
            }
            return netData;
        },
        // 手风琴切换
        toggleGroup(index) {
            this.list[index].open = !this.list[index].open;
        },
        toggleSubGroup(parentIdx, subIdx) {
            this.list[parentIdx].list[subIdx].open = !this.list[parentIdx].list[subIdx].open;
        },
        // 处理选中/取消选中
        handleCheck(item) {
            if (item.id == this.userInfo.id) return;
            if (this.checkedUser && this.checkedUser.id === item.id) {
                this.checkedUser = null;
            } else {
                this.checkedUser = item;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fbfbfb;
    min-height: 100vh;

    .search {
        padding: 24rpx;
        background: #fff;

        .search-box {
            padding: 20rpx 0;
            border-radius: 12rpx;
            background: #f7f8fa;
        }
    }

    .groups {
        .group-item {
            padding: 20rpx;
            border-radius: 12rpx;
            background: #fff;
            margin-bottom: 10rpx;

            &:last-child {
                margin-bottom: 0;
            }

            .user {
                &:first-child {
                    margin-top: 16rpx;
                }

                .user-avatar {
                    position: relative;

                    .user-status {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        background: #fd0100;
                        border-radius: 16rpx 0rpx 16rpx 0rpx;
                        padding: 4rpx 8rpx;
                    }
                }

                .checkbox {
                    .uncheck-box {
                        width: 44rpx;
                        height: 44rpx;
                        border-radius: 8rpx;
                        border: 1rpx solid #000;
                        background: #e6e6e6;
                    }

                    .check-box {
                        width: 44rpx;
                        height: 44rpx;
                        border-radius: 4rpx;
                        background: #fd0100;
                        position: relative;

                        &::before {
                            content: "";
                            position: absolute;
                            top: 8rpx;
                            left: 6rpx;
                            transform: rotate(-45deg);
                            width: 65%;
                            height: 30%;
                            border-radius: 4rpx;
                            border-bottom: 4rpx solid #fff;
                            border-left: 4rpx solid #fff;
                        }
                    }
                }

                // .action-box {
                //     .btn {
                //         padding: 16rpx 22rpx;
                //         border-radius: 8rpx;
                //         border: 1rpx solid #e6e6e6;
                //     }
                // }
            }

            .expand {
                .list {
                    margin-top: 16rpx;
                }
            }
        }

        .user-item {
            padding: 20rpx;
            border-radius: 12rpx;
            background: #fff;
            margin-bottom: 10rpx;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .checkbox {
            .uncheck-box {
                width: 44rpx;
                height: 44rpx;
                border-radius: 8rpx;
                border: 1rpx solid #000;
                background: #e6e6e6;
            }

            .check-box {
                width: 44rpx;
                height: 44rpx;
                border-radius: 4rpx;
                background: #fd0100;
                position: relative;

                &::before {
                    content: "";
                    position: absolute;
                    top: 8rpx;
                    left: 6rpx;
                    transform: rotate(-45deg);
                    width: 65%;
                    height: 30%;
                    border-radius: 4rpx;
                    border-bottom: 4rpx solid #fff;
                    border-left: 4rpx solid #fff;
                }
            }
        }
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: #fbfbfb;
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);

        .btn {
            width: 100%;
            height: 100%;
            background: #fd0100;
            border-radius: 12rpx;
        }
    }
}
</style>
