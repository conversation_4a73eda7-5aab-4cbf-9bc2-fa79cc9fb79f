<template>
    <view class="view">
        <!-- <section class="bg">
            <image class="bg-img" src="/static/common/office-bg.png" mode="scaleToFill" />
        </section> -->
        <section class="navbar">
            <c-navBar isPerch title="资金绩效数据统计" :isBack="true" backgroundColor="#FD0200" color="#fff"> </c-navBar>
        </section>
        <section class="filter gap-14">
            <view class="filter-item flex-1 flex align-center justify-between" @click="showDate = true">
                <text class="mf-font-24" style="color: #999999">日期</text>
                <u-input
                    :value="$u.timeFormat(form.date, 'yyyy-mm')"
                    disabled
                    fontSize="24rpx"
                    color="#333"
                    disabledColor="transparent"
                    inputAlign="right"
                    suffixIcon="arrow-right"
                    border="none"
                    :suffixIconStyle="{ fontSize: '24rpx' }"
                    placeholder="请选择日期"
                />
            </view>
            <view class="filter-item flex-1 flex align-center justify-between">
                <text class="mf-font-24" style="color: #999999">占比</text>
                <u-input
                    v-model="form.rate"
                    color="#333"
                    fontSize="24rpx"
                    inputAlign="right"
                    border="none"
                    placeholder="请输入占比"
                    suffixIcon="%"
                    :suffixIconStyle="{ fontSize: '24rpx' }"
                    @change="handleChangeRate"
                />
            </view>
            <view class="filter-item flex-1 flex align-center justify-between" @click="showTown = true">
                <text class="mf-font-24" style="color: #999999">市/镇/村</text>
                <view class="flex align-center gap-12">
                    <text class="mf-font-24" style="color: #333333" v-if="form.town_id">{{ form.town_id.town_name }}</text>
                    <text class="mf-font-24" style="color: #333333" v-else>全部</text>
                    <u-icon name="arrow-right" size="24rpx"></u-icon>
                </view>
            </view>
            <view class="filter-item flex-1 flex align-center justify-between">
                <text class="mf-font-24" style="color: #999999">只看未达标</text>
                <u-switch
                    v-model="form.only_not_compliance"
                    activeColor="#FD0100"
                    inactiveColor="#999999"
                    size="16"
                    @change="handleChangeOnlyNotCompliance"
                />
            </view>
        </section>

        <section class="cards">
            <scroll-view scroll-y :style="{ height: scrollViewHeight }">
                <view class="card" v-for="(item, key) in list" :key="key">
                    <view class="flex align-center justify-between">
                        <view class="flex flex-col gap-16">
                            <text class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ item.title }}</text>
                            <text class="mf-font-28" style="color: #999999">{{ `${item.used_total}/${item.total}` }}</text>
                        </view>
                    </view>
                    <view class="circle-progress-wrapper">
                        <c-circleProgress
                            :percentage="item.used_rate"
                            :size="230"
                            :stroke-width="15"
                            stroke-color="#EBB84C"
                            background-color="#FFF2ED"
                            text-color="#1A1A1A"
                            sub-text-color="#999999"
                        ></c-circleProgress>
                    </view>
                    <view class="progres-item flex flex-col gap-16" v-for="(children, index) in item.child" :key="index">
                        <view class="mf-font-24 mf-weight-bold" style="color: #333333">{{ children.type_name }}</view>
                        <view class="flex align-center justify-between">
                            <text class="mf-font-20" style="color: #666666">{{ `${children.used_total}/${children.total}` }}</text>
                            <text class="mf-font-20" style="color: #333333">{{ children.used_rate_tag }}</text>
                        </view>
                        <view class="flex-1">
                            <u-line-progress
                                :percentage="children.used_rate"
                                height="14rpx"
                                :showText="false"
                                inactive-color="#FFF5F5"
                                active-color="#FD0100"
                            ></u-line-progress>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </section>
        <u-datetime-picker
            :show="showDate"
            :value="form.date"
            close-on-click-overlay
            @confirm="handleConfirmDate"
            @close="showDate = false"
            @cancel="showDate = false"
            mode="year-month"
        ></u-datetime-picker>
        <u-picker
            :show="showTown"
            :columns="[townColumns]"
            keyName="village_name"
            close-on-click-overlay
            @confirm="handleConfirmTown"
            @close="showTown = false"
            @cancel="showTown = false"
        ></u-picker>
    </view>
</template>

<script>
export default {
    data() {
        return {
            proportion: 0,
            showDate: false, // 日期选择
            showTown: false, // 城镇选择
            modelVale: 60,
            target: 100,
            form: {
                date: "", // 日期
                only_not_compliance: false, // 未达标 false 0 true 1
                level: 0, // 层级
                town_id: "", // 城镇id/村id/市id
                rate: 30, // 占比
                page: 1,
                page_size: 10,
                parent_type: 1,
                type: "",
            },
            list: [], // 资金绩效列表
            townColumns: [], // 城镇列表
        };
    },
    computed: {
        progressPercentage() {
            // 计算进度百分比，默认为85%
            return this.proportion ? Number(this.proportion) : 85;
        },
        scrollViewHeight() {
            // 计算滚动视图高度，提高兼容性
            const baseHeight = this.form.level == 0 ? 380 : 480;
            // 提供多种兼容性方案
            if (typeof window !== "undefined" && window.innerHeight) {
                // Web环境使用px计算
                const windowHeight = window.innerHeight;
                const offsetHeight = baseHeight * (windowHeight / 750); // rpx转px的大概比例
                return `${windowHeight - offsetHeight}px`;
            } else {
                // 小程序环境使用calc
                return `calc(100vh - ${baseHeight}rpx)`;
            }
        },
    },
    onLoad(options) {
        this.form.level = options.level;
        this.form.parent_type = options.parent_type;
        this.form.date = this.$u.timeFormat(new Date().getTime(), "yyyy-mm");
        this.getFundsStatistics();
    },
    methods: {
        // 选择城镇
        handleConfirmTown(e) {
            this.form.town_id = e.value[0];
            this.form.page = 1;
            this.list = [];
            this.getFundsStatistics();
            this.showTown = false;
        },
        // 选择只看未达标
        handleChangeOnlyNotCompliance(e) {
            this.form.page = 1;
            this.getFundsStatistics();
        },
        // 选择占比
        handleChangeRate() {
            this.form.page = 1;
            uni.$u.throttle(this.getFundsStatistics, 500, false);
        },
        // 选择日期
        handleConfirmDate(e) {
            this.form.page = 1;
            this.form.date = this.$u.timeFormat(e.value, "yyyy-mm");
            this.showDate = false;
            this.getFundsStatistics();
        },
        // 获取资金绩效统计
        async getFundsStatistics() {
            try {
                const formData = {
                    ...this.form,
                    town_id: this.form.town_id ? this.form.town_id.id : "",
                    only_not_compliance: this.form.only_not_compliance ? 1 : 0,
                };
                const res = await this.$api.fundsStatistics(formData);
                if (res.code === 200) {
                    if (this.form.level == 1) {
                        this.list = res.data.list;
                        this.townColumns = res.data.village;
                    } else if (this.form.level == 2) {
                        this.list = res.data;
                    } else if (this.form.level == 3) {
                        this.list = res.data;
                    }
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取资金绩效时发生错误");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fbfbfb;
    min-height: 100vh;
    .filter {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        padding: 16rpx 24rpx;
        background: #fff5f5;
        .filter-item {
            height: 60rpx;
            border: 1rpx solid #999999;
            border-radius: 8rpx;
            padding: 12rpx 18rpx;
        }
    }
    .cards {
        padding: 32rpx 24rpx;
        .card {
            background: #fff;
            border-radius: 16rpx;
            padding: 24rpx;
            box-shadow: 0rpx 12rpx 12rpx -4rpx rgba(224, 224, 224, 0.28);
            margin-top: 24rpx;
            &:first-child {
                margin-top: 0;
            }
            .circle-progress-wrapper {
                margin: 40rpx auto;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .progres-item {
                margin-top: 32rpx;
                &:first-child {
                    margin-top: 0;
                }
            }
        }
    }
}
</style>
