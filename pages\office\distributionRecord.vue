<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar isPerch title="发放记录" isBack> </c-navBar>
        </section>
        <section class="list" v-if="userSentLogData.length > 0">
            <view class="item">
                <view class="flex align-center gap-12">
                    <view class="mf-font-32 mf-weight-bold" style="color: #333">驻村补助发放记录</view>
                    <view class="status mf-font-20" style="color: #fff">已发放</view>
                </view>
                <view class="flex flex-col gap-16" style="margin-top: 20rpx">
                    <view class="mf-font-24" style="color: #999">补助总金额：2333.44元</view>
                    <view class="mf-font-24" style="color: #999">发放时间：2333.44元</view>
                    <view class="mf-font-24" style="color: #999">补助天数：2333.44元</view>
                    <view class="mf-font-24" style="color: #999">请假天数：2333.44元</view>
                    <view class="mf-font-24" style="color: #999">旷工：2333.44元</view>
                    <view class="mf-font-24" style="color: #999">补助时间：2025-05-21 至 2025-05-21</view>
                </view>
            </view>
        </section>
        <section class="empty flex flex-center" style="height: 300rpx" v-else>
            <u-empty mode="list" icon="list" text="暂无数据" />
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            userSentLogData: [], // 驻村补助记录
            userInfo: {}, // 用户信息
            page: 1, // 页码
        };
    },
    onShow() {
        this.handleGetUserSentLog();
    },
    onLoad(options) {
        this.userInfo = uni.getStorageSync("userInfo");
    },
    methods: {
        // 获取驻村补助记录
        async handleGetUserSentLog() {
            try {
                const res = await this.$api.userSentLog({
                    user_id: this.userInfo.id,
                    page: this.page,
                });
                if (res.code === 200) {
                    this.userSentLogData = res.data.list;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取驻村补助记录失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fbfbfb;
    min-height: 100vh;
    .list {
        padding: 24rpx;
        .item {
            background: #fff;
            border-radius: 12rpx;
            padding: 20rpx 24rpx;
            margin-top: 24rpx;

            &:first-child {
                margin-top: 0;
            }
            .status {
                padding: 6rpx 12rpx;
                border-radius: 12rpx 0 12rpx 0;
                background: #fd0100;
            }
        }
    }
}
</style>
