@import '../../../assets/styles/common';
@import './web';
@import './h5';
@import './uni';
@import './wx';

:not(not) {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  min-width: 0;
}

/* 响应式适配，确保工具栏展开时消息列表正确调整 */
.tui-chat {
  &-message-list {
    transition: margin-bottom 0.3s ease;
  }
  
  /* 当工具栏展开时，输入框上移，工具栏在最底部 */
  &.toolbar-expanded {
    .tui-chat-message-input,
    .tui-chat-h5-message-input,
    .tui-chat-uni-message-input {
      bottom: 220px !important;
    }
    
    .tui-chat-message-input-toolbar,
    .tui-chat-h5-message-input-toolbar,
    .tui-chat-uni-message-input-toolbar,
    .message-input-toolbar-uni {
      bottom: 0 !important;
    }
  }
}

/* 确保固定定位的元素在小屏幕设备上正确显示 */
@media (max-width: 768px) {
  .tui-chat-message-input,
  .tui-chat-h5-message-input,
  .tui-chat-uni-message-input {
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .tui-chat-message-input-toolbar,
  .tui-chat-h5-message-input-toolbar,
  .tui-chat-uni-message-input-toolbar {
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  }
}
