// 封装的人脸识别函数
import { jumpPage } from "./uni.js";
import { imageToBase64 } from "./common.js";
export async function recognizeFace(imageUrl) {
    try {
        // 构建发送给腾讯云的 payload
        // GroupIds 需替换为实际的人员库ID，可以通过参数传递或配置文件管理
        // imageUrl 可以是 base64 或图片URL，优先使用 base64
        let payload = {
            "GroupIds": ["staff_group"], // TODO: 替换为实际人员库ID
            "MaxFaceNum": 1, // 检测最大人脸数
            "MinFaceSize": 40, // 最小人脸尺寸
            "MaxPersonNum": 1, // 返回最相似的人员数
            "NeedPersonInfo": 1, // 是否需要返回人员信息
            "QualityControl": 2 // 图片质量控制
        };
        // 判断 imageUrl 是否为 base64 格式
        if (typeof imageUrl === 'string' && imageUrl.startsWith('data:image')) {
            payload.Image = imageUrl;
        } else {
            payload.Url = imageUrl;
        }

        console.log('正在调用云函数 search-faces...');
        const res = await uniCloud.callFunction({
            name: 'search-faces',
            data: {
                payload: payload
            }
        });

        console.log('云函数返回结果:', res);

        // 处理返回结果
        if (res.result) {
            const responseData = res.result.Response;
            if (responseData && responseData.Error) {
                console.error('腾讯云API错误:', responseData.Error);
                return {
                    succeed: false,
                    msg: `识别失败: ${responseData.Error.Message}`
                };
            } else if (responseData && responseData.Results && responseData.Results.length > 0 && responseData.Results[0].Candidates.length > 0) {
                const topCandidate = responseData.Results[0].Candidates[0];
                return {
                    succeed: true,
                    msg: `识别成功! 最匹配的人员是：${topCandidate.PersonName}，相似度：${topCandidate.Score}`,
                    score: topCandidate.Score
                };
            } else {
                return {
                    succeed: false,
                    msg: '未在人员库中匹配到相似人员'
                };
            }
        } else {
            const errMsg = res.result ? res.result.errMsg : '未知错误';
            console.error('云函数执行失败:', errMsg);
            return {
                succeed: false,
                msg: `识别失败: ${errMsg}`
            };
        }

    } catch (recErr) {
        console.error('调用云函数异常:', recErr);
        return {
            succeed: false,
            msg: `识别请求失败: ${recErr.message}`
        };
    }
}

/**
 * @description 启动腾讯云活体人脸核身
 * @param {string} ruleId - 在腾讯云控制台配置的核验规则ID
 * @returns {Promise<object>}
 */
export function startFaceVerify(ruleId) {
    if (!ruleId) {
        return Promise.resolve({
            succeed: false,
            msg: '缺少核验规则ID (RuleId)'
        });
    }

    return new Promise((resolve) => {
        const userInfo = uni.getStorageSync('userInfo');
        console.log('userInfo', userInfo.avatar);
        uni.downloadFile({
            url: userInfo.avatar,
            success: async (downloadRes) => {
                console.log('Download result:', downloadRes);
                if (downloadRes.statusCode === 200) {
                    try {
                        let base64 = await imageToBase64(downloadRes.tempFilePath);
                        base64 = base64.split('base64,')[1]
                        console.log('正在调用云函数 init-face-verify...');
                        const cloudRes = await uniCloud.callFunction({
                            name: 'init-face-verify',
                            data: {
                                payload: {
                                    RuleId: ruleId,
                                    IdCard: userInfo.idcard,
                                    Name: userInfo.nickname,
                                    ImageBase64: base64,
                                    RedirectUrl: 'https://face.tnew.tech/',
                                }
                            }
                        })
                        if (cloudRes.result && cloudRes.result.Response && !cloudRes.result.Response.Error) {
                            const url = encodeURIComponent(cloudRes.result.Response.Url);
                            jumpPage(`/pages/common/webview?url=${url}`, true);
                            resolve({ succeed: true, msg: '人脸核身启动 成功' }); // 表示启动成功
                        } else {
                            const errMsg = cloudRes.result.Response.Error.Message || '启动核验失败';
                            resolve({ succeed: false, msg: errMsg });
                        }

                    } catch (e) {
                        console.error('Error after download:', e);
                        resolve({ succeed: false, msg: `处理失败: ${e.message}` });
                    }
                } else {
                    resolve({ succeed: false, msg: `头像下载失败: ${downloadRes.statusCode}` });
                }
            },
            fail: (error) => {
                console.error('下载失败:', error);
                resolve({ succeed: false, msg: '头像下载失败' });
            }
        });
    });
}
