<template>
    <view class="view flex flex-col">
        <section class="navbar">
            <c-navBar isPerch title="签名签署" :isBack="true"></c-navBar>
        </section>
        <section class="tips">注：请在签名区域内签名，请勿超出签名区域范围。</section>
        <section class="canvas-wrapper flex-1">
            <canvas
                class="signature-canvas"
                :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
                canvas-id="signatureCanvas"
                @touchstart="onTouchStart"
                @touchmove="onTouchMove"
                @touchend="onTouchEnd"
            >
                <view v-if="!isSigned" class="placeholder">请在此区域内签名</view>
            </canvas>
        </section>
        <section class="footer flex">
            <button class="reset-btn flex-1" @click="resetSignature">重签</button>
            <button class="confirm-btn flex-1" @click="confirmSignature">确定</button>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            canvasContext: null, // 画布上下文
            isSigned: false, // 是否签名
            canvasWidth: 0, // 画布宽度
            canvasHeight: 0, // 画布高度
            points: [], // 点数组
            lastPoint: null, // 最后一点
        };
    },
    onReady() {
        // 获取画布宽高
        const query = uni.createSelectorQuery().in(this);
        query
            .select(".canvas-wrapper")
            .boundingClientRect((data) => {
                if (data) {
                    this.canvasWidth = data.width;
                    this.canvasHeight = data.height;
                    this.canvasContext = uni.createCanvasContext("signatureCanvas", this);
                    this.canvasContext.setLineWidth(4);
                    this.canvasContext.setLineCap("round");
                    this.canvasContext.setLineJoin("round");
                    this.canvasContext.setStrokeStyle("#000000");
                }
            })
            .exec();
    },
    methods: {
        // 触摸开始
        onTouchStart(e) {
            this.isSigned = true;
            const { x, y } = e.changedTouches[0];
            this.lastPoint = { x, y };
            this.canvasContext.beginPath();
        },
        // 触摸移动
        onTouchMove(e) {
            const { x, y } = e.changedTouches[0];
            const currentPoint = { x, y };

            const midPoint = {
                x: (this.lastPoint.x + currentPoint.x) / 2,
                y: (this.lastPoint.y + currentPoint.y) / 2,
            };

            this.canvasContext.moveTo(this.lastPoint.x, this.lastPoint.y);
            this.canvasContext.quadraticCurveTo(currentPoint.x, currentPoint.y, midPoint.x, midPoint.y);

            this.canvasContext.stroke();
            this.canvasContext.draw(true);

            this.lastPoint = midPoint;
        },
        // 触摸结束
        onTouchEnd() {
            this.canvasContext.draw(true);
        },
        // 返回
        goBack() {
            uni.navigateBack();
        },
        // 重签
        resetSignature() {
            this.isSigned = false;
            this.points = [];
            this.canvasContext.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
            this.canvasContext.draw();
        },
        // 确定
        confirmSignature() {
            if (!this.isSigned) {
                uni.showToast({
                    title: "请先签名",
                    icon: "none",
                });
                return;
            }
			// 生成签名图片
            uni.canvasToTempFilePath(
                {
                    canvasId: "signatureCanvas",
                    fileType: "png",
                    success: (res) => {
                        const eventChannel = this.getOpenerEventChannel();
                        if (eventChannel && eventChannel.emit) {
                            eventChannel.emit("onSignComplete", { signaturePath: res.tempFilePath });
                        }
                        uni.navigateBack();
                    },
                    fail: () => {
                        uni.showToast({
                            title: "生成签名图片失败",
                            icon: "none",
                        });
                    },
                },
                this
            );
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    height: 100vh;
    background-color: #f5f5f5;
    box-sizing: border-box;
    .tips {
        padding: 10px 15px;
        font-size: 14px;
        color: #e60012;
        flex-shrink: 0;
    }

    .canvas-wrapper {
        margin: 0 15px;
        background-color: #fff;
        border: 1px dashed #ccc;
        border-radius: 8px;
        position: relative;
        overflow: hidden;

        .signature-canvas {
            width: 100%;
            height: 100%;
        }

        .placeholder {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ccc;
            font-size: 16px;
        }
    }

    .footer {
        display: flex;
        padding: 15px;
        background-color: #f5f5f5;
        margin-top: 10px;
        flex-shrink: 0;

        .reset-btn {
            margin-right: 10px;
            background-color: #fff;
            border: 1px solid #ccc;
            color: #333;
            line-height: 40px;
            height: 40px;
        }

        .confirm-btn {
            background-color: #e60012;
            color: #fff;
            line-height: 40px;
            height: 40px;
        }
    }
}
</style>
