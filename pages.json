{
    "easycom": {
        "^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue",
        "^c-(.*)": "@/components/c-$1/c-$1.vue"
    },
    "pages": [
        {
            "path": "pages/tabbar/home",
            "style": {
                "navigationBarTitleText": "",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/checkIn/supplementClock",
            "style": {
                "navigationBarTitleText": "补卡",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/checkIn/statistics",
            "style": {
                "navigationBarTitleText": "统计",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/checkIn/settings",
            "style": {
                "navigationBarTitleText": "设置",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/tabbar/office",
            "style": {
                "navigationBarTitleText": "",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/tabbar/addressBook",
            "style": {
                "navigationBarTitleText": "",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/addressBook/search",
            "style": {
                "navigationBarTitleText": "",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/addressBook/organizationalStructure-children",
            "style": {
                "navigationBarTitleText": "驻村办",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/addressBook/message",
            "style": {
                "navigationBarTitleText": "私信",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/tabbar/login",
            "style": {
                "navigationBarTitleText": "登录",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/tabbar/mine",
            "style": {
                "navigationBarTitleText": "个人中心",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/tabbar/change-pwd",
            "style": {
                "navigationBarTitleText": "设置密码",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/tabbar/changePass",
            "style": {
                "navigationBarTitleText": "设置密码",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/common/webview",
            "style": {
                "navigationBarTitleText": "webview",
                "navigationStyle": "custom"
            }
        }
    ],
    "subPackages": [
        {
            "root": "pages/office",
            "pages": [
                {
                    "path": "sign",
                    "style": {
                        "navigationBarTitleText": "",
                        "enablePullDownRefresh": false,
                        "navigationStyle": "custom",
                        "pageOrientation": "landscape"
                    }
                },
                {
                    "path": "financialPerformance",
                    "style": {
                        "navigationBarTitleText": "资金绩效",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "financialPerformanceDetail",
                    "style": {
                        "navigationBarTitleText": "资金绩效",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "dataStatistics",
                    "style": {
                        "navigationBarTitleText": "资金绩效数据统计",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "dataStatisticsChildren",
                    "style": {
                        "navigationBarTitleText": "资金绩效数据统计",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "dispatch",
                    "style": {
                        "navigationBarTitleText": "调度",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "dispatch-children",
                    "style": {
                        "navigationBarTitleText": "调度",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "initiateScheduling",
                    "style": {
                        "navigationBarTitleText": "发起调度",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "database",
                    "style": {
                        "navigationBarTitleText": "资料库",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "detail",
                    "style": {
                        "navigationBarTitleText": "资料详情",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "unreadList",
                    "style": {
                        "navigationBarTitleText": "未读列表",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "leaveApplication",
                    "style": {
                        "navigationBarTitleText": "请假申请",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "leaveRecord",
                    "style": {
                        "navigationBarTitleText": "请假记录",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "leaveDetail",
                    "style": {
                        "navigationBarTitleText": "请假详情",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "applyList",
                    "style": {
                        "navigationBarTitleText": "审批列表",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "applyDetail",
                    "style": {
                        "navigationBarTitleText": "审批详情",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "villageSubsidy",
                    "style": {
                        "navigationBarTitleText": "驻村补助",
                        "navigationStyle": "custom",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "distributionRecord",
                    "style": {
                        "navigationBarTitleText": "发放记录",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "vehicleManagement",
                    "style": {
                        "navigationBarTitleText": "车辆管理",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "emergencyDispatchOfVehicles",
                    "style": {
                        "navigationBarTitleText": "紧急派车",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "reportingForGoingOut",
                    "style": {
                        "navigationBarTitleText": "外出报备",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "vehicleDispatch",
                    "style": {
                        "navigationBarTitleText": "车辆派遣",
                        "navigationStyle": "custom"
                    }
                }
            ]
        },
        {
            "root": "pages/mine",
            "pages": [
                {
                    "path": "collectionMaterials",
                    "style": {
                        "navigationBarTitleText": "收藏资料",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "report",
                    "style": {
                        "navigationBarTitleText": "举报投诉",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "information",
                    "style": {
                        "navigationBarTitleText": "个人主页",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "settings",
                    "style": {
                        "navigationBarTitleText": "设置",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "change-pwd",
                    "style": {
                        "navigationBarTitleText": "设置密码",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "agreement",
                    "style": {
                        "navigationBarTitleText": "协议/关于我们",
                        "navigationStyle": "custom"
                    }
                }
            ]
        },
        {
            "root": "TUIKit",
            "pages": [
                {
                    "path": "components/TUIConversation/index",
                    "style": {
                        "navigationBarTitleText": ""
                    }
                },
                {
                    "path": "components/TUIChat/index",
                    "style": {
                        "navigationBarTitleText": "",
                        "app-plus": {
                            "softinputMode": "adjustResize",
                            "titleNView": {
                                "buttons": [
                                    {
                                        "type": "menu"
                                    }
                                ]
                            }
                        },
                        "h5": {
                            "titleNView": {
                                "buttons": []
                            }
                        }
                    }
                },
                // 集成 chat 组件，必须配置该路径: 视频播放
                {
                    "path": "components/TUIChat/video-play",
                    "style": {
                        "navigationBarTitleText": ""
                    }
                },
                {
                    "path": "components/TUIChat/web-view",
                    "style": {
                        "navigationBarTitleText": ""
                    }
                },
                {
                    "path": "components/TUIContact/index",
                    "style": {
                        "navigationBarTitleText": ""
                    }
                },
                {
                    "path": "components/TUIGroup/index",
                    "style": {
                        "navigationBarTitleText": ""
                    }
                },
                {
                    "path": "components/TUISearch/index",
                    "style": {
                        "navigationBarTitleText": "聊天记录"
                    }
                }
            ]
        }
    ],
    "preloadRule": {
        "pages/tabbar/addressBook": {
            "network": "all",
            "packages": ["TUIKit"]
        }
    },
    "globalStyle": {
        "navigationBarTextStyle": "black",
        "navigationBarTitleText": "uni-app",
        "navigationBarBackgroundColor": "#F8F8F8",
        "backgroundColor": "#F8F8F8",
        "app-plus": {
            "bounce": "none"
        }
    },
    "uniIdRouter": {},
    "tabBar": {
        "color": "#595959",
        "selectedColor": "#EC3015",
        "backgroundColor": "#FFFFFF",
        "borderStyle": "black",
        "list": [
            {
                "selectedIconPath": "/static/tabbar/tabbar1-active.png",
                "iconPath": "/static/tabbar/tabbar1.png",
                "pagePath": "pages/tabbar/home",
                "text": "领补助"
            },
            {
                "selectedIconPath": "/static/tabbar/tabbar2-active.png",
                "iconPath": "/static/tabbar/tabbar2.png",
                "pagePath": "pages/tabbar/office",
                "text": "办公"
            },
            {
                "selectedIconPath": "/static/tabbar/tabbar3-active.png",
                "iconPath": "/static/tabbar/tabbar3.png",
                "pagePath": "pages/tabbar/addressBook",
                "text": "通讯录"
            },
            {
                "selectedIconPath": "/static/tabbar/tabbar4-active.png",
                "iconPath": "/static/tabbar/tabbar4.png",
                "pagePath": "pages/tabbar/mine",
                "text": "我的"
            }
        ]
    }
}
