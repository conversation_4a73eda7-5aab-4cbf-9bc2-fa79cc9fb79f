<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch :title="data.framework_label || data.town_name || '加载中...'" :isBack="true" color="#000"> </c-navBar>
        </section>
        <section class="groups">
            <view v-if="currentPath.length > 0" class="back-item flex align-center" @click="goBack">
                <u-icon name="arrow-left" size="32rpx" style="margin-right: 16rpx"></u-icon>
                <text class="mf-font-32">返回上级</text>
            </view>
            <!-- 市驻村办 -->
            <view
                class="group-item flex align-center gap-24"
                v-if="data.framework_label === '市驻村办'"
                v-for="item in data.user"
                :key="item.id"
            >
                <view class="user flex-1 flex align-center justify-between" @click="handleCheck(item)">
                    <view class="flex align-center gap-24">
                        <view class="user-avatar">
                            <u-image
                                :src="item.avatar || '/static/common/group-icon2.png'"
                                width="128rpx"
                                height="128rpx"
                                radius="16rpx"
                                mode="scaleToFill "
                            ></u-image>
                            <view class="user-status mf-font-20" style="color: #fff">{{ item.online_status.name }}</view>
                        </view>
                        <view class="flex flex-col gap-30">
                            <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ item.nickname }}</view>
                            <view class="mf-font-28" style="color: #ebb84c">{{ item.role_name || "驻村队长" }}</view>
                        </view>
                    </view>
                    <view class="action-box">
                        <view class="btn mf-font-28" style="color: #1a1a1a" @click.stop="$fn.jumpPage('/pages/addressBook/message')">
                            发私信
                        </view>
                    </view>
                </view>
            </view>
            <!-- 派出单位 -->
            <view
                class="group-item flex align-center gap-24"
                v-if="data.framework_label === '派出单位'"
                v-for="item in data.user"
                :key="item.id"
            >
                <view class="user flex-1 flex align-center justify-between" @click="handleCheck(item)">
                    <view class="flex align-center gap-24">
                        <view class="user-avatar">
                            <u-image
                                :src="item.avatar || '/static/common/group-icon2.png'"
                                width="128rpx"
                                height="128rpx"
                                radius="16rpx"
                                mode="scaleToFill "
                            ></u-image>
                            <view class="user-status mf-font-20" style="color: #fff">{{ item.online_status.name }}</view>
                        </view>
                        <view class="flex flex-col gap-30">
                            <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ item.nickname }}</view>
                            <view class="mf-font-28" style="color: #ebb84c">{{ item.role_name || "驻村队长" }}</view>
                        </view>
                    </view>
                    <view class="action-box">
                        <view class="btn mf-font-28" style="color: #1a1a1a" @click.stop="$fn.jumpPage('/pages/addressBook/message')">
                            发私信
                        </view>
                    </view>
                </view>
            </view>
            <!-- 乡镇/下钻通用 -->
            <view v-if="currentLevelList.length > 0">
                <view
                    class="group-item flex align-center gap-24"
                    v-for="(item, index1) in currentLevelList"
                    :key="index1"
                >
                    <!-- group类型 -->
                    <view
                        v-if="item.user"
                        class="group"
                        @click="goToChildren(item)"
                    >
                        <u-image src="/static/common/group-icon2.png" width="72rpx" height="72rpx" radius="8rpx" mode="aspectFill"></u-image>
                        <view class="flex align-center justify-between flex-1">
                            <text class="mf-font-32 mf-weight-bold">{{ item.framework_label || item.name || '加载中...' }}</text>
                            <u-icon name="arrow-right" size="32rpx"></u-icon>
                        </view>
                    </view>
                    <!-- user类型 -->
                    <view
                        v-else
                        class="user flex-1 flex align-center justify-between"
                        @click="handleCheck(item)"
                    >
                        <view class="flex align-center gap-24">
                            <view class="user-avatar">
                                <u-image
                                    :src="item.avatar || '/static/mine/avatar.png'"
                                    width="128rpx"
                                    height="128rpx"
                                    radius="16rpx"
                                    mode="scaleToFill"
                                ></u-image>
                                <view class="user-status mf-font-20" style="color: #fff">{{ item.online_status.name }}</view>
                            </view>
                            <view class="flex flex-col gap-30">
                                <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ item.nickname }}</view>
                                <view class="mf-font-28" style="color: #ebb84c">{{ item.role_name || item.role || '加载中...' }}</view>
                            </view>
                        </view>
                        <view class="action-box">
                            <view class="btn mf-font-28" style="color: #1a1a1a" @click.stop="$fn.jumpPage('/pages/addressBook/message')">
                                发私信
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            api: () => {},
            apiParams: {},
            checkedUser: null, // 改为单选，只存储一个选中的用户
            currentPath: [], // 当前路径，用于记录导航层级
            currentLevelList: [], // 当前层级列表，动态渲染
            data: {},
        };
    },
    onLoad(options) {
        this.$nextTick(() => {
            this.data = JSON.parse(options.data);
            this.currentLevelList = this.data.role_group || [];
            console.log(this.currentLevelList);
            
        });
    },
    computed: {
    },
    methods: {
        // 检查是否选中
        isChecked(item) {
            return this.checkedUser && this.checkedUser.id === item.id;
        },
        // 处理选中/取消选中
        handleCheck(item) {
            if (this.checkedUser && this.checkedUser.id === item.id) {
                this.checkedUser = null; // 如果已选中，则取消选中
            } else {
                this.checkedUser = item; // 否则选中当前项
            }
        },
        // 进入下一级
        goToChildren(role) {
            if (role.user && role.user.length > 0) {
                this.currentPath.push(this.currentLevelList); // 记录当前层级
                this.currentLevelList = role.user; // 进入下一级
            }
        },
        // 返回上一级
        goBack() {
            if (this.currentPath.length > 0) {
                this.currentLevelList = this.currentPath.pop(); // 返回上一级
            }
        },
        // 发起调度
        submitDispatch() {
            if (!this.checkedUser) {
                uni.showToast({
                    title: "请选择调度人员",
                    icon: "none",
                });
                return;
            }
            console.log("选中的人员:", this.checkedUser);
            // 这里可以添加提交调度的逻辑
            this.$fn.jumpPage("/pages/office/initiateScheduling");
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fbfbfb;
    min-height: 100vh;
    padding-bottom: 150rpx;

    .groups {
        .back-item {
            padding: 30rpx 32rpx;
            background: #fff;
            margin-bottom: 10rpx;
            border-radius: 12rpx;
        }

        .group-item {
            padding: 40rpx 32rpx;
            border-radius: 12rpx;
            background: #fff;
            margin-bottom: 10rpx;
            &:last-child {
                margin-bottom: 0;
            }

            .group {
                display: flex;
                align-items: center;
                gap: 24rpx;
                width: 100%;
            }

            .user {
                .user-avatar {
                    position: relative;
                    .user-status {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        background: #fd0100;
                        border-radius: 16rpx 0rpx 16rpx 0rpx;
                        padding: 4rpx 8rpx;
                    }
                }
                .action-box {
                    .btn {
                        padding: 16rpx 22rpx;
                        border-radius: 8rpx;
                        border: 1rpx solid #e6e6e6;
                    }
                }
            }
        }
    }
}
</style>
