'use strict';
const crypto = require('crypto');

// TODO: 请在腾讯云人脸核身控制台的规则配置中获取，并在此处填入
const WBESigNatureSecret = "Your-WBESigNatureSecret-Here";

exports.main = async (event, context) => {
    console.log('Received callback event:', JSON.stringify(event));

    // uniCloud HTTP 触发器会将原始 body 转为字符串，这里需要解析
    let body;
    try {
        body = JSON.parse(event.body);
    } catch (e) {
        console.error("Failed to parse request body:", e);
        return {
            statusCode: 400,
            body: 'Bad Request: Invalid JSON format.'
        };
    }
    
    // 1. 签名验证
    const {
        sign,
        ...dataToVerify
    } = body;
    if (!sign) {
        return {
            statusCode: 401,
            body: 'Unauthorized: Missing signature.'
        };
    }

    const calculatedSign = calculateSignature(dataToVerify, WBESigNatureSecret);
    if (calculatedSign !== sign) {
        console.error("Signature verification failed. Expected:", calculatedSign, "Received:", sign);
        return {
            statusCode: 403,
            body: 'Forbidden: Invalid signature.'
        };
    }
    console.log("Signature verification succeeded.");


    // 2. 处理业务逻辑
    if (body.code === '0') {
        console.log(`Face verification successful for order number: ${body.orderNo}`);
        // 核身成功
        try {
            // TODO: 在这里实现您的打卡签到逻辑
            // 1. 从数据库或缓存中，根据 body.orderNo (订单号) 或您在启动核身时传递的 extra 参数，获取用户信息和打卡所需数据。
            //    例如：const clockInData = await db.collection('clock_tasks').doc(body.orderNo).get();
            
            // 2. 调用您的打卡API
            //    注意：在云函数中，您需要使用 uniCloud.callFunction 调用其他云函数或使用封装好的数据库、API请求方法，
            //    而不是像前端那样使用 this.$api。
            //    例如: const clockResult = await uniCloud.callFunction({ name: 'your-clock-api-function', data: { ... } });
            toBack();
            console.log("打卡业务逻辑处理成功！");

        } catch (bizError) {
            console.error("Business logic processing failed:", bizError);
            // 即使业务失败，也应告诉腾讯云已收到，避免重发
        }

    } else {
        // 核身失败
        console.warn(`Face verification failed. Code: ${body.code}, Message: ${body.msg}`);
    }


    // 3. 返回成功响应给腾讯云
    // 腾讯云要求返回特定的成功格式
    return {
        statusCode: 200,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            "code": 0,
            "msg": "ok"
        })
    };
};


/**
 * 计算腾讯云回调签名
 * @param {object} params - 回调的body内容（除了sign本身）
 * @param {string} secret - 签名密钥
 * @returns {string} - 计算出的签名
 */
function calculateSignature(params, secret) {
    // 1. 按key字典序排序
    const sortedKeys = Object.keys(params).sort();
    
    // 2. 拼接成 key=value&key=value 格式
    const stringToSign = sortedKeys.map(key => `${key}=${params[key]}`).join('&');

    // 3. HMAC-SHA256 加密
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(stringToSign);
    
    // 4. Base64 编码
    return hmac.digest('base64');
} 

function toBack() {
    uni.navigateBack();
}