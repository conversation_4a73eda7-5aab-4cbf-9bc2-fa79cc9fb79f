.tui-chat {
  width: 100%;
  height: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;

  &-default {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
  }

  &-header {
    padding: 10px;
    box-sizing: border-box;
    display: flex;
  }

  &-message-list {
    flex: 1;
    overflow: hidden;
    display: flex;
    margin-bottom: 160px; /* 为底部输入区域预留空间 */
    transition: margin-bottom 0.3s ease;
  }

  &-leave-group {
    font-size: 14px;
    height: 160px;
    border-top: 1px solid #efefef;
    justify-content: center;
    align-items: center;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    z-index: 1000;

    &-mobile {
      height: 50px;
    }
  }

  &-message-input {
    height: 160px;
    display: flex;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    z-index: 1001;
    border-top: 1px solid #efefef;
  }

  &-message-input-toolbar {
    position: fixed;
    bottom: -220px; /* 默认隐藏在输入框下方 */
    left: 0;
    right: 0;
    background: white;
    z-index: 1000;
    border-top: 1px solid #efefef;
    height: 220px;
    transition: bottom 0.3s ease;
    
    /* 当工具栏显示时 */
    &.show {
      bottom: 0;
    }
  }

  /* 当工具栏展开时，输入框上移，消息列表预留更多空间 */
  &.toolbar-expanded {
    .tui-chat-message-input {
      bottom: 220px;
    }
    
    .tui-chat-message-list {
      margin-bottom: 380px; /* 输入框160px + 工具栏220px */
    }
  }
}
