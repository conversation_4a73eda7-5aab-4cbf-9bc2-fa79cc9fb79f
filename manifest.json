{
    "name" : "米林驻村APP",
    "appid" : "__UNI__BC1FC04",
    "description" : "",
    "versionName" : "1.0.5",
    "versionCode" : 105,
    "transformPx" : false,
    "sassImplementationName" : "node-sass",
    "globalStyle" : {
        "fontFamily" : "FZXiaoBiaoSong-B05S"
    },
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Maps" : {},
            "Geolocation" : {},
            "Camera" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.POST_NOTIFICATIONS\"/>"
                ],
                "schemes" : "milin"
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "maps" : {
                    "amap" : {
                        "name" : "amapBCDDOU5ug",
                        "appkey_ios" : "265fac6ddbfd803fcc93617f3daef87d",
                        "appkey_android" : "265fac6ddbfd803fcc93617f3daef87d"
                    }
                },
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "android" ]
                    },
                    "amap" : {
                        "name" : "amapBCDDOU5ug",
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "265fac6ddbfd803fcc93617f3daef87d",
                        "appkey_android" : "265fac6ddbfd803fcc93617f3daef87d"
                    }
                },
                "push" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        },
        "nativePlugins" : {
            "TencentCloud-TUICallKit" : {
                "__plugin_info__" : {
                    "name" : "【官方】腾讯云音视频通话插件TencentCloud-TUICallKit",
                    "description" : "TUICallKit 是腾讯云官方推出的音视频通话插件，支持 1v1 通话和群组通话，并提供“类微信\"的 UI 交互，开发者仅需三个 API 就可实现。",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=9035",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "9035",
                    "parameters" : {}
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx84da649b86c3bbf2",
        "permission" : {
            "scope.userLocation" : {
                "desc" : "初始化时获取用户的位置信息用于外出打卡定位"
            }
        },
        "requiredPrivateInfos" : [ "getLocation", "chooseLocation" ],
        "setting" : {
            "urlCheck" : false,
            "minified" : true
        },
        "optimization" : {
            "subPackages" : true
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "2",
    "h5" : {
        "devServer" : {
            "disableHostCheck" : true,
            "proxy" : {
                "/api" : {
                    "target" : "https://mf8.mufengweilai.com/api/class-plaque",
                    "changeOrigin" : true,
                    "secure" : false,
                    "pathRewrite" : {
                        "^/api" : ""
                    }
                }
            }
        },
        "router" : {
            "base" : "/web/class-plaque/h5"
        }
    }
}
/* ios打包配置 *//* SDK配置 *//* 快应用特有相关 *//* 小程序特有相关 */

