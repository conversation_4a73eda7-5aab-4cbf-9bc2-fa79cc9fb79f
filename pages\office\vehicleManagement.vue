<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="车辆管理" isBack></c-navBar>
        </section>
        <section class="tabs">
            <u-tabs
                :list="tabs"
                :scrollable="false"
                activeStyle="color: #FD0100; font-weight: bold;"
                lineColor="#FD0100"
                @click="handleChangeTab"
            ></u-tabs>
        </section>
        <section class="filter flex align-center justify-between gap-16">
            <view class="flex align-center gap-16">
                <view v-if="selectedFilterName">
                    <view class="tag active mf-font-24">{{ selectedFilterName }}</view>
                </view>
            </view>
            <view class="flex align-center" @click="showFilterPopup = true">
                <text class="mf-font-28" style="color: #999">筛选</text>
                <view style="margin-top: 8rpx">
                    <u-image src="/static/common/filter.png" width="48rpx" height="48rpx" mode="aspectFill"></u-image>
                </view>
            </view>
        </section>
        <section class="list">
            <scroll-view scroll-y class="scroll-view" style="height: calc(100vh - 420rpx)" @scrolltolower="handleScrollToBottom">
                <view class="card-item" v-for="(item, index) in useList" :key="index">
                    <view class="card-header flex align-center gap-16">
                        <view class="number mf-font-28 mf-weight-bold" style="background: #fd0100">1</view>
                        <view class="flex-1 flex align-center justify-between">
                            <view class="flex align-center gap-20">
                                <text class="mf-font-28" style="color: #fd0100">{{ item.type_tag.name }}</text>
                                <view class="tag mf-font-24" style="background: #f3f3f3; color: #fd0100" v-if="item.trip_status_tag">
                                    {{ item.trip_status_tag.name }}
                                </view>
                            </view>
                            <text class="mf-font-24" :style="{ color: item.status_tag.color }">{{ item.status_tag.name }}</text>
                        </view>
                    </view>
                    <view class="card-content">
                        <view class="flex align-center gap-40" style="color: #1a1a1a">
                            <text class="mf-font-28">车牌：{{ item.driver_info.car_no }}</text>
                            <text class="mf-font-28">乘客：{{ item.user ? item.user.nickname : '--' }}</text>
                        </view>
                        <view class="flex flex-col gap-12" style="color: #666666; margin-top: 20rpx">
                            <view class="flex align-center gap-12">
                                <u-icon name="map" size="30rpx" color="#FD0100"></u-icon>
                                <text class="mf-font-24">出发地点：{{ item.set_out_place }}</text>
                            </view>
                            <view class="flex align-center gap-12">
                                <u-icon name="map" size="30rpx" color="#3198FF"></u-icon>
                                <text class="mf-font-24">到达地点：{{ item.destination }}</text>
                            </view>
                        </view>
                    </view>
                    <view class="card-info" style="color: #999">
                        <view class="content flex flex-col gap-16">
                            <text class="mf-font-24">离村时间：{{ item.go_time }}</text>
                            <text class="mf-font-24">
                                负责司机：{{ item.driver_info.driver_name }} {{ item.driver_info.driver_mobile }}
                            </text>
                            <view class="actions flex align-center gap-32" style="color: #fd0100">
                                <!-- <view class="action mf-font-24" @click="handleCallPhone(item.user.mobile)">联系乘客</view> -->
                                <view class="action mf-font-24" @click.stop="handleCallPhone(item.driver_info)">联系司机</view>
                            </view>
                        </view>
                    </view>
                </view>
                <u-loadmore :status="status" marginBottom="24rpx" />
                <view class="placeholder" style="height: 24rpx"></view>
            </scroll-view>
        </section>
        <!-- 悬浮按钮 -->
        <c-movable-area :initialX="300" :initialY="680">
            <view class="btn" @click="$fn.jumpPage('/pages/office/emergencyDispatchOfVehicles', true)">
                <u-image src="/static/common/car.png" width="128rpx" height="128rpx" shape="circle" mode="aspectFill"></u-image>
                <text class="text mf-font-24" style="color: #fff">紧急派车</text>
            </view>
        </c-movable-area>
        <!-- 筛选弹窗 -->
        <u-popup
            mode="bottom"
            :show="showFilterPopup"
            @close="showFilterPopup = false"
            close-on-click-overlay
            safe-area-inset-bottom
            round="32rpx"
        >
            <view class="filter-popup">
                <view class="filter-popup-header flex flex-center">
                    <text class="mf-font-36 mf-weight-bold" style="color: #333333">筛选条件</text>
                    <view class="close" @click="showFilterPopup = false">
                        <u-icon name="close" size="32rpx" color="#333333"></u-icon>
                    </view>
                </view>
                <view class="filter-popup-content">
                    <view class="filter-popup-item">
                        <text class="label mf-font-28 mf-weight-bold">派车类型</text>
                        <view class="filter-popup-item-content flex flex-wrap gap-32" style="margin-top: 20rpx">
                            <view
                                class="filter-popup-item-content-item"
                                v-for="(item, index) in filterOptions.type"
                                :key="index"
                                :class="{ active: filterForm.type === item.value }"
                                @click="filterForm.type = item.value"
                            >
                                <text class="mf-font-28">{{ item.name }}</text>
                            </view>
                        </view>
                    </view>
                    <view class="filter-popup-item">
                        <text class="label mf-font-28 mf-weight-bold">行程状态</text>
                        <view class="filter-popup-item-content flex flex-wrap gap-32" style="margin-top: 20rpx">
                            <view
                                class="filter-popup-item-content-item"
                                v-for="(item, index) in filterOptions.trip_status"
                                :key="index"
                                :class="{ active: filterForm.trip_status === item.value }"
                                @click="filterForm.trip_status = item.value"
                            >
                                <text class="mf-font-28">{{ item.name }}</text>
                            </view>
                        </view>
                    </view>
                    <!-- <view class="filter-popup-item">
                        <text class="label mf-font-28 mf-weight-bold">乡镇类型</text>
                        <view class="filter-popup-item-content flex flex-wrap gap-32" style="margin-top: 20rpx">
                            <view
                                class="filter-popup-item-content-item"
                                v-for="(item, index) in townshipType"
                                :key="index"
                                :class="{ active: filterForm.chooseTownshipType.includes(item) }"
                                @click="handleChooseTownshipType(item)"
                            >
                                <text class="mf-font-28">{{ item }}</text>
                            </view>
                        </view>
                    </view> -->
                    <!-- <view class="filter-popup-item">
                        <text class="label mf-font-28 mf-weight-bold">时间</text>
                        <view class="filter-popup-item-content flex flex-wrap gap-32" style="margin-top: 20rpx">
                            <view
                                class="filter-popup-item-content-item"
                                v-for="(item, index) in timeType"
                                :key="index"
                                :class="{ active: filterForm.chooseTimeType.includes(item) }"
                                @click="handleChooseTimeType(item)"
                            >
                                <text class="mf-font-28">{{ item }}</text>
                            </view>
                        </view>
                    </view> -->
                    <view class="filter-popup-item">
                        <text class="label mf-font-28 mf-weight-bold">自定义时间</text>
                        <view class="choose-date flex align-center gap-16" style="margin-top: 20rpx">
                            <view class="choose-date-item flex-1 flex align-center justify-between" @click="chooseDateTime('start')">
                                <text class="mf-font-24" style="color: #333333">
                                    {{ $u.timeFormat(filterForm.start_time, "yyyy-mm-dd hh:MM") || "开始时间" }}
                                </text>
                                <u-icon name="calendar" size="32rpx" color="#333333"></u-icon>
                            </view>
                            <view class="line"></view>
                            <view class="choose-date-item flex-1 flex align-center justify-between" @click="chooseDateTime('end')">
                                <text class="mf-font-24" style="color: #333333">
                                    {{ $u.timeFormat(filterForm.end_time, "yyyy-mm-dd hh:MM") || "结束时间" }}
                                </text>
                                <u-icon name="calendar" size="32rpx" color="#333333"></u-icon>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="filter-popup-footer flex align-center justify-between">
                    <view class="filter-popup-footer-item flex flex-1 flex-center one" @click="resetFilter">
                        <text class="mf-font-28 mf-weight-bold">重置</text>
                    </view>
                    <view class="filter-popup-footer-item flex flex-1 flex-center two" @click="confirmFilter">
                        <text class="mf-font-28 mf-weight-bold">确认</text>
                    </view>
                </view>
            </view>
        </u-popup>

        <!-- 日期选择器 -->
        <u-datetime-picker
            :show="showDatePicker"
            :value="currentDate"
            closeOnClickOverlay
            mode="datetime"
            @confirm="confirmDateTime"
            @close="showDatePicker = false"
            @cancel="showDatePicker = false"
        ></u-datetime-picker>
    </view>
</template>

<script>
export default {
    data() {
        return {
            showFilterPopup: false,
            currentDate: new Date().getTime(), // 当前日期
            topTags: {
                emergencyDispatchValue: null, // "紧急派车"对应的type value
                tripAbnormalValue: null, // "行程异常"对应的trip_status value
            },
            status: "loadmore", // 加载状态
            filterOptions: {
                type: [],
                status: [],
                trip_status: [],
            },
            useList: [], // 用车列表
            total: 0, // 总条数
            filterForm: {
                page: 1,
                page_size: 10,
                car_id: "", // 车辆id
                type: "", // 选中的派车类型（单值）
                status: "", // 选中的车辆状态（单值）
                trip_status: "", // 选中的行程状态（单值）
                start_time: "", // 开始时间
                end_time: "", // 结束时间
                town_id: "", // 乡镇id 未启用
                village_id: "", // 村id 未启用
                mobile: "", // 手机号 未启用
                chooseTownshipType: "", // 选中的乡镇类型 未启用
                chooseTimeType: "", // 选中的时间类型 未启用
            },
            tabs: [
                {
                    name: "全部",
                    value: 0,
                },
                {
                    name: "待接客",
                    value: 1,
                },
                {
                    name: "进行中",
                    value: 2,
                },
                {
                    name: "已完成",
                    value: 3,
                },
            ],
            showDatePicker: false,
            dateType: "", // 时间类型
        };
    },
    computed: {
        selectedFilterName() {
            // 只显示type和trip_status中已选的name，优先type
            if (this.filterForm.type && this.filterOptions.type) {
                const found = this.filterOptions.type.find((item) => item.value === this.filterForm.type);
                if (found) return found.name;
            }
            if (this.filterForm.trip_status && this.filterOptions.trip_status) {
                const found = this.filterOptions.trip_status.find((item) => item.value === this.filterForm.trip_status);
                if (found) return found.name;
            }
            return "";
        },
    },
    onLoad() {
        this.handleGetUseList();
    },
    methods: {
        // 滚动到底部
        handleScrollToBottom() {
            if (this.useList.length >= this.total) {
                this.$fn.showToast("没有更多数据了");
                return;
            }
            this.filterForm.page++;
            this.handleGetUseList();
        },
        // 切换tab
        handleChangeTab(tab) {
            this.useList = [];
            this.filterForm.page = 1;
            this.filterForm.status = tab.value;
            this.handleGetUseList();
        },
        // 筛选
        confirmFilter() {
            this.useList = [];
            this.filterForm.page = 1;
            this.showFilterPopup = false;
            this.handleGetUseList();
        },
        // 获取用车列表
        async handleGetUseList() {
            try {
                if (this.status === "loading") return;
                this.status = "loading";
                const res = await this.$api.getUseList(this.filterForm);
                if (res.code === 200) {
                    this.filterOptions = res.data.filter;
                    this.useList = [...this.useList, ...res.data.list];
                    console.log(this.useList);

                    this.total = res.data.total_count;
                    if (this.useList.length >= this.total) {
                        this.status = "nomore";
                    } else {
                        this.status = "loadmore";
                    }
                }
            } catch (error) {
                console.log(error);
            }
        },
        // 自定义时间
        chooseDateTime(type) {
            this.showDatePicker = true;
            this.dateType = type;
        },
        // 确认时间
        confirmDateTime(value) {
            if (this.dateType === "start") {
                this.filterForm.start_time = value.value;
            } else {
                this.filterForm.end_time = value.value;
            }
            this.showDatePicker = false;
        },
        // 重置筛选
        resetFilter() {
            this.filterForm.type = "";
            this.filterForm.trip_status = "";
            this.filterForm.chooseTownshipType = "";
            this.filterForm.chooseTimeType = "";
            this.filterForm.start_time = "";
            this.filterForm.end_time = "";
        },

        // 顶部标签点击
        handleTopTagClick(type, value) {
            if (!value) return;
            this.filterForm[type] = this.filterForm[type] === value ? "" : value;
        },
        // 拨打电话
        async handleCallPhone(driver_info) {
            try {
                if (driver_info && driver_info.driver_mobile) {
                    await this.$fn.callPhone(driver_info.driver_mobile);
                } else {
                    this.$fn.showToast("司机号码不存在，无法拨打");
                }
            } catch (error) {
                console.log(error);
                // this.$fn.showToast("拨打电话失败：" + error.message);
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #f1f1f1;
    .tabs {
        background: #fff5f5;
    }
    .filter {
        padding: 24rpx;
        .tag {
            padding: 8rpx 20rpx;
            background: #f3f3f3;
            color: #333333;
            border-radius: 8rpx;
        }
        .active {
            color: #fd0100;
        }
    }
    .list {
        padding: 0 24rpx;
        .card-item {
            border-radius: 16rpx;
            background: #fff;
            margin-top: 24rpx;
            &:first-child {
                margin-top: 0;
            }
            .card-header {
                padding: 24rpx;
                border-bottom: 1rpx dashed #e6e9f0;
                .number {
                    border-radius: 16rpx 0 16rpx 0;
                    padding: 8rpx 34rpx;
                    color: #fff;
                }
                .tag {
                    padding: 8rpx 20rpx;
                    background: #f3f3f3;
                    border-radius: 8rpx;
                }
            }
            .card-content {
                padding: 24rpx;
            }
            .card-info {
                padding: 0 24rpx 24rpx 24rpx;
                .content {
                    padding: 24rpx;
                    border-radius: 16rpx;
                    background: #f7f8fa;
                    .actions {
                        .action {
                            border-radius: 8rpx;
                            padding: 6rpx 12rpx;
                            border: 2rpx solid #fd0100;
                        }
                    }
                }
            }
        }
    }
    .btn {
        position: relative;
        width: 128rpx;
        height: 128rpx;
        border-radius: 50%;
        box-shadow: 0 10rpx 30rpx #fd010060;
        .text {
            position: absolute;
            bottom: 20%;
            left: 0;
            width: 100%;
            text-align: center;
        }
    }
    .filter-popup {
        border-radius: 32rpx 32rpx 0rpx 0rpx;
        .filter-popup-header {
            position: relative;
            padding: 36rpx;
            .close {
                position: absolute;
                right: 24rpx;
                top: 24rpx;
            }
        }
        .filter-popup-content {
            padding: 0 24rpx 24rpx 24rpx;
            .filter-popup-item {
                margin-top: 64rpx;
                &:first-child {
                    margin-top: 0;
                }
                .filter-popup-item-content {
                    .filter-popup-item-content-item {
                        padding: 8rpx 20rpx;
                        background: #f3f3f3;
                        border-radius: 8rpx;
                        color: #333333;
                    }
                    .active {
                        background: #fd0100;
                        color: #fff;
                    }
                }
            }
            .choose-date {
                margin-top: 32rpx;
                .choose-date-item {
                    padding: 18rpx;
                    background: #f3f3f3;
                    border-radius: 8rpx;
                }
                .line {
                    width: 30rpx;
                    height: 2rpx;
                    background: #e6e9f0;
                }
            }
        }
        .filter-popup-footer {
            padding: 24rpx;
            .filter-popup-footer-item {
                padding: 24rpx 0;
                &.one {
                    background: #eeeef0;
                    color: #666666;
                    border-radius: 12rpx 0rpx 0rpx 12rpx;
                }
                &.two {
                    background: #fd0100;
                    color: #ffffff;
                    border-radius: 0rpx 12rpx 12rpx 0rpx;
                }
            }
        }
    }
}
</style>
