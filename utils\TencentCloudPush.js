import * as Push from '@/uni_modules/TencentCloud-Push';
// 腾讯云推送
const PushSDKAppID = 1600090099; // 您的 SDKAppID
const PushAppKey = 'VJEORYYfoLSpOoWG0nLBqOJljxK4Ytrurjs6ROVPlaryZNtELNI64Oo8JdSitLRi'; // 客户端密钥
// 服务端密钥：86272731e1cdbac9a2ffbbdfef8e295ba0a88836eb3475bffd2db73e1422302c
// 客户端密钥:VJEORYYfoLSpOoWG0nLBqOJljxK4Ytrurjs6ROVPlaryZNtELNI64Oo8JdSitLRi
async function initTencentCloudPush() {
    uni.$push = Push; // 腾讯云推送
    uni.$PushSDKAppID = PushSDKAppID; // 腾讯云推送SDKAppID
    uni.$PushAppKey = PushAppKey; // 腾讯云推送AppKey
    const userInfo = uni.getStorageSync("userInfo");
    if (!userInfo || !userInfo.id) {
        console.error("【init】腾讯云推送: 无法获取用户信息，初始化中止。请确保用户已登录。");
        return;
    }
    await uni.$push.setRegistrationID(String(userInfo.id), () => {
        console.log("【init】腾讯云推送: 设置注册ID成功");
    })
    await handlePushRegister(); // 腾讯云推送注册
    await handleNotificationClicked(); // 监听通知栏点击事件，获取推送扩展信息
    await handleMessageReceived(); // 监听在线推送
    await handleMessageRevoked(); // 监听在线推送被撤回
}


async function handlePushRegister() {
    await uni.$push.registerPush(PushSDKAppID, PushAppKey, (data) => {
        console.log("【init】腾讯云推送: 注册成功", data);
    }, (error, msg) => {
        console.error('【init】腾讯云推送: 注册失败', error, msg);
    })
}


// 监听通知栏点击事件，获取推送扩展信息
async function handleNotificationClicked(res) {
    await uni.$push.addPushListener(Push.EVENT.NOTIFICATION_CLICKED, (res) => {
        console.log('【init】腾讯云推送: 监听通知栏点击事件，获取推送扩展信息', res);
    })
}

// 监听在线推送
async function handleMessageReceived(res) {
   await uni.$push.addPushListener(Push.EVENT.MESSAGE_RECEIVED, (res) => {
        // res 为消息内容
        console.log('【init】腾讯云推送: 监听在线推送', res);
    })
}

// 监听在线推送被撤回
async function handleMessageRevoked(res) {
    await uni.$push.addPushListener(Push.EVENT.MESSAGE_REVOKED, (res) => {
        // res 为被撤回的消息 ID
        console.log('【init】腾讯云推送: 监听在线推送被撤回', res);
    })
}

export { initTencentCloudPush };