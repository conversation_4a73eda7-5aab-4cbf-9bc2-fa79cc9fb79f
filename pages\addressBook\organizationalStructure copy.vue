<template>
    <view class="view">
        <section class="groups">
            <scroll-view scroll-y class="scroll-view" style="height: calc(100vh - 390rpx)">
                <!-- 市驻村办 -->
                <view
                    class="group-item flex align-center gap-24"
                    @click="
                        $fn.jumpPage(
                            `/pages/addressBook/organizationalStructure-children?data=${JSON.stringify(frameworkData.country_group)}`,
                        )
                    "
                >
                    <u-image src="/static/common/group-icon2.png" width="72rpx" height="72rpx" radius="8rpx" mode="aspectFill"></u-image>
                    <view class="flex align-center justify-between flex-1">
                        <text class="mf-font-32 mf-weight-bold">{{ frameworkData.country_group.framework_label || "加载中..." }}</text>
                        <u-icon name="arrow-right" size="32rpx"></u-icon>
                    </view>
                </view>
                <!-- 派出单位 -->
                <view
                    class="group-item flex align-center gap-24"
                    @click="
                        $fn.jumpPage(`/pages/addressBook/organizationalStructure-children?data=${JSON.stringify(frameworkData.send_group)}`)
                    "
                >
                    <u-image src="/static/common/group-icon2.png" width="72rpx" height="72rpx" radius="8rpx" mode="aspectFill"></u-image>
                    <view class="flex align-center justify-between flex-1">
                        <text class="mf-font-32 mf-weight-bold">{{ frameworkData.send_group.framework_label || "加载中..." }}</text>
                        <u-icon name="arrow-right" size="32rpx"></u-icon>
                    </view>
                </view>
                <!-- 乡镇 -->
                <view
                    class="group-item flex align-center gap-24"
                    v-for="item in frameworkData.town"
                    @click="$fn.jumpPage(`/pages/addressBook/organizationalStructure-children?data=${JSON.stringify(item)}`)"
                >
                    <u-image src="/static/common/group-icon2.png" width="72rpx" height="72rpx" radius="8rpx" mode="aspectFill"></u-image>
                    <view class="flex align-center justify-between flex-1">
                        <text class="mf-font-32 mf-weight-bold">{{ item.town_name || "加载中..." }}</text>
                        <u-icon name="arrow-right" size="32rpx"></u-icon>
                    </view>
                </view>
            </scroll-view>
        </section>
    </view>
</template>

<script>
export default {
    name: "organizationalStructure",
    data() {
        return {
            frameworkData: {},
        };
    },
    methods: {
        // 获取组织架构列表
        async handleGetFrameworkList() {
            try {
                const res = await this.$api.getFrameworkList();
                if (res.code === 200) {
                    this.frameworkData = res.data;
                    this.$emit("frameworkDataNum", res.data.village_total_user);
                    this.dataCollation(res.data);
                }
            } catch (error) {
                console.log(error);
            }
        },

        // 数据整理
        async dataCollation(data) {
            let netData = [];
            // 遍历所有数据
            for (const [key, value] of Object.entries(data)) {
                // 处理市级和派驻组数据
                if (typeof value === "object" && !Array.isArray(value) && value !== null) {
                    if (value.user?.length > 0) {
                        netData.push({
                            group_name: value.framework_label,
                            user: value.user,
                        });
                    }
                }

                // 处理乡镇数据（乡镇下有村，村下才是用户）
                if (Array.isArray(value)) {
                    value.forEach((ele) => {
                        netData.push({
                            group_name: ele.town_name,
                            list: ele.role_group.map((e) => {
                                return {
                                    group_name: e.framework_label,
                                    user: e.user,
                                };
                            }),
                        });
                    });
                }
            }
            console.log(netData);

            return netData;
        },
    },
};
</script>

<style lang="scss">
.view {
    .groups {
        .group-item {
            padding: 40rpx 32rpx;
            border-radius: 12rpx;
            background: #fff;
            margin-bottom: 10rpx;
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}
</style>
