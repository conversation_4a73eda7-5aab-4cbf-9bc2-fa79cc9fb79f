<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="审批列表" :isBack="true"> </c-navBar>
        </section>
        <section class="tabs">
            <u-tabs :list="tabs" activeStyle="color: #FD0100; font-weight: bold;" lineColor="#FD0100" :scrollable="false" @click="handleClick"></u-tabs>
        </section>
        <section class="list" v-if="filterList.length > 0">
            <scroll-view 
                scroll-y 
                style="height: calc(100vh - 300rpx)"
                refresher-enabled="true"
                :refresher-triggered="refresherTriggered"
                @refresherrefresh="onRefresh"
            >
                <view class="list-item flex align-start justify-between" v-for="item in filterList" :key="item.id" @click="
                    $fn.jumpPage(`/pages/office/applyDetail?id=${item.apply.id}&type=${item.apply.type ? item.apply.type.name : ''}`)
                    ">
                    <view class="left flex flex-col gap-16">
                        <view class="flex align-center gap-22" v-if="item.apply">
                            <text calss="mf-font-32 mf-weight-bold" style="color: #333">
                                {{ item.apply.nickname }}-{{ item.apply.type ? item.apply.type.name : "" }}
                            </text>
                            <view class="status mf-font-20 clear-tag" v-if="item.clear_tag" style="color: #fff">
                                {{ item.clear_tag.name }}
                            </view>
                            <view v-else class="status mf-font-20" style="color: #fff">
                                {{ item.status ? item.status.name : "" }}
                            </view>
                        </view>
                        <view class="flex flex-col gap-8 mf-font-24" style="color: #999999" v-if="item.apply">
                            <text>开始时间：{{ item.apply.start_date }}</text>
                            <text>结束时间：{{ item.apply.end_date }}</text>
                            <text>申请人：{{ item.apply.nickname }}</text>
                            <text>申请时间：{{ item.apply.create_time }}</text>
                        </view>
                    </view>
                    <view class="right">
                        <u-image src="/static/common/status1.png" v-if="item.status.value === 1" width="120rpx" height="120rpx" mode="aspectFill"></u-image>
                        <u-image src="/static/common/status2.png" v-if="item.status.value === 2" width="120rpx" height="120rpx" mode="aspectFill"></u-image>
                        <u-image src="/static/common/status3.png" v-if="item.status.value === 3" width="120rpx" height="120rpx" mode="aspectFill"></u-image>
                        <u-image src="/static/common/status4.png" v-if="item.status.value === 0" width="120rpx" height="120rpx" mode="aspectFill"></u-image>
                    </view>
                </view>
            </scroll-view>
        </section>
        <section class="empty flex flex-center" v-else>
            <u-empty text="暂无记录" />
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            type: "leave",
            status: -1,
            tabs: [
                { name: "全部", value: "" },
                { name: "待审批", value: 0 },
                { name: "已同意", value: 1 },
                { name: "已驳回", value: 2 },
                { name: "已查阅", value: 3 },
            ],
            list: [],
            filterList: [],
            userInfo: {},
            refresherTriggered: false,
        };
    },
    onLoad(options) {
        this.handleGetMyApply();
        this.userInfo = uni.getStorageSync("userInfo");
    },
    methods: {
        handleClick(tab) {
            this.handleGetMyApply({ approve_status: tab.value });
        },
        onRefresh() {
            this.refresherTriggered = true;
            this.handleGetMyApply();
        },
        // 获取待审批数据
        async handleGetMyApply(approve_status) {
            try {
                const res = await this.$api.myApply(approve_status);
                if (res.code === 200) {
                    this.list = res.data.list; // 全部数据(备用)
                    this.filterList = res.data.list; // 全部数据(当前页面展示的数据)
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取待审批数据失败");
            } finally {
                setTimeout(() => {
                    this.refresherTriggered = false;
                }, 2000);
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .tabs {
        background: #fff5f5;
    }

    .list {
        padding: 24rpx;
        background-color: #fbfbfb;

        .list-item {
            padding: 24rpx 20rpx;
            background: #ffffff;
            border-radius: 12rpx;
            margin-top: 24rpx;

            &:first-child {
                margin-top: 0;
            }

            .left {
                .status {
                    padding: 4rpx 10rpx;
                    background: #fd0100;
                    border-radius: 12rpx 0rpx 12rpx 0rpx;
                }

                .clear-tag {
                    background: #fd7840;
                }
            }
        }
    }

    .empty {
        height: calc(100vh - 300rpx);
        background-color: #fbfbfb;
    }
}
</style>
