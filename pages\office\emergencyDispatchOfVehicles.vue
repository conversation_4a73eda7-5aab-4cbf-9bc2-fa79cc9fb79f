<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="紧急派车" isPerch isBack> </c-navBar>
        </section>
        <section class="content">
            <u--form
                labelPosition="left"
                :model="form"
                :rules="rules"
                ref="uForm"
                labelWidth="220rpx"
                :labelStyle="{ color: '#1a1a1a', fontWeight: 'bold' }"
                errorType="toast"
            >
                <view class="form">
                    <u-form-item label="乘客" prop="passenger" borderBottom @click="showPassengerPopup = true">
                        <u-input
                            v-model="choosePassenger.label"
                            disabled
                            disabledColor="transparent"
                            inputAlign="right"
                            suffixIcon="arrow-right"
                            border="none"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            placeholder="请选择乘客"
                        />
                    </u-form-item>
                    <u-form-item label="司机" prop="driver" borderBottom @click="showDriverPicker = true">
                        <u-input
                            v-model="chooseDriver.label"
                            disabled
                            disabledColor="transparent"
                            inputAlign="right"
                            suffixIcon="arrow-right"
                            border="none"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            placeholder="请选择司机"
                        />
                    </u-form-item>
                    <u-form-item label="离村时间" prop="leaveDate" borderBottom @click="showDatePicker = true">
                        <u-input
                            :value="form.leaveDate"
                            disabled
                            disabledColor="transparent"
                            inputAlign="right"
                            suffixIcon="arrow-right"
                            border="none"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            placeholder="请选择离村时间"
                        />
                    </u-form-item>
                    <u-form-item label="出发地" prop="startPlace" labelPosition="top">
                        <view class="textarea-box">
                            <u--textarea
                                v-model="form.startPlace"
                                placeholderStyle="color: #999"
                                count
                                border="none"
                                height="300rpx"
                                maxlength="80"
                                placeholder="请输入出发地"
                            ></u--textarea>
                        </view>
                    </u-form-item>
                    <u-form-item label="目的地" prop="endPlace" labelPosition="top">
                        <view class="textarea-box">
                            <u--textarea
                                v-model="form.endPlace"
                                placeholderStyle="color: #999"
                                count
                                border="none"
                                height="300rpx"
                                maxlength="80"
                                placeholder="请输入目的地"
                            ></u--textarea>
                        </view>
                    </u-form-item>
                </view>
            </u--form>
        </section>
        <section class="footer">
            <view class="btn flex flex-center mf-font-36 mf-weight-bold" style="color: #fff" @click="handleSubmit">提交</view>
        </section>

        <u-datetime-picker
            :show="showDatePicker"
            :value="currentDate"
            :minDate="minDate"
            close-on-click-overlay
            @confirm="handleConfirmDate"
            @close="showDatePicker = false"
            @cancel="showDatePicker = false"
            mode="date"
        ></u-datetime-picker>
        <u-picker
            :show="showDriverPicker"
            :columns="[driver]"
            keyName="label"
            @confirm="handleConfirmDriverPicker"
            @close="showDriverPicker = false"
            @cancel="showDriverPicker = false"
            close-on-click-overlay
        ></u-picker>
        <!-- 乘客选择弹窗 -->
        <u-popup :show="showPassengerPopup" @close="showPassengerPopup = false" mode="bottom" round="12">
            <view class="passenger-popup">
                <view class="search-box">
                    <u-input
                        v-model="keywords"
                        placeholder="搜索乘客"
                        prefixIcon="search"
                        border="none"
                        style="margin-bottom: 24rpx; background: #f7f8fa; border-radius: 8rpx"
                        @confirm="handleSearchUserlist"
                        @input="handleSearchUserlist"
                    />
                </view>
                <scroll-view style="height: calc(50vh - 100rpx)" scroll-y>
                    <u-empty v-if="passengerList.length === 0" mode="list" text="暂无数据"></u-empty>
                    <view
                        v-for="item in passengerList"
                        :key="item.value"
                        @click="handleSelectPassenger(item)"
                        style="padding: 24rpx 0; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center"
                    >
                        <view style="flex: 1; color: #1a1a1a; font-size: 32rpx">{{ item.label }}</view>
                        <u-icon name="checkbox-mark" v-if="form.passenger.value === item.value" color="#fd0100" size="36rpx" />
                    </view>
                </scroll-view>
            </view>
        </u-popup>
    </view>
</template>

<script>
export default {
    data() {
        return {
            form: {
                passenger: "",
                driver: "",
                leaveDate: "",
                startPlace: "",
                endPlace: "",
            },
            rules: {
                passenger: [{ required: true, type: "number", message: "请选择乘客" }],
                driver: [{ required: true, type: "number", message: "请选择司机" }],
                leaveDate: [{ required: true, type: "string", message: "请选择离村时间" }],
                startPlace: [{ required: true, type: "string", message: "请输入出发地" }],
                endPlace: [{ required: true, type: "string", message: "请输入目的地" }],
            },
            choosePassenger: {}, // 已选择的乘客
            chooseDriver: {}, // 已选择的司机
            showDatePicker: false, // 是否显示日期选择器
            minDate: new Date().getTime(), // 最小日期
            currentDate: "", // 当前日期
            showDriverPicker: false, // 是否显示司机选择器
            columns: [], // 选择器数据
            driver: [], // 司机列表
            showPassengerPopup: false, // 乘客弹窗
            keywords: "", // 乘客搜索关键词
            passengerList: [], // 乘客列表
        };
    },
    onReady() {
        this.$refs.uForm.setRules(this.rules);
    },
    onLoad(options) {
        this.handleGetCarList();
    },
    methods: {
        // 获取司机列表
        async handleGetCarList() {
            try {
                const res = await this.$api.getCarList({ page: 1 });
                if (res.code === 200) {
                    this.driver = res.data.list.map((item) => ({ label: `${item.driver_name}(${item.status_tag.name})`, value: item.id }));
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取司机列表失败");
            }
        },
        // 搜索用户列表
        async handleSearchUserlist() {
            try {
                uni.showLoading({
                    title: "搜索中...",
                    mask: true,
                });
                const res = await this.$api.getUserList({ keywords: this.keywords });
                if (res.code === 200) {
                    this.passengerList = res.data.list.map((item) => ({ label: item.nickname, value: item.id }));
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("搜索用户列表失败");
            } finally {
                uni.hideLoading();
            }
        },
        // 确认日期
        handleConfirmDate(value) {
            this.form.leaveDate = this.$u.timeFormat(value.value, "yyyy-mm-dd");
            this.showDatePicker = false;
        },
        // 新增：选择乘客
        handleSelectPassenger(item) {
            this.choosePassenger = item;
            this.form.passenger = item.value;
            this.showPassengerPopup = false;
        },
        // 确认司机
        handleConfirmDriverPicker(item) {
            this.chooseDriver = item.value[0];
            this.form.driver = item.value[0].value;
            this.showDriverPicker = false;
        },
        // 提交
        handleSubmit() {
            this.$refs.uForm.validate().then(async (verification) => {
                if (verification) {
                    try {
                        const res = await this.$api.urgentSentCar({
                            car_id: this.form.driver,
                            user_id: this.form.passenger,
                            go_time: this.form.leaveDate,
                            set_out_place: this.form.startPlace,
                            destination: this.form.endPlace,
                        });
                        if (res.code === 200) {
                            this.$fn.showToast("提交成功");
                            this.$fn.jumpBack();
                        }
                    } catch (error) {
                        console.error(error);
                        this.$fn.showToast("提交失败");
                    }
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        padding-bottom: 164rpx;
        .form {
            padding: 24rpx 24rpx 0 24rpx;
            background-color: #fff;
            border-radius: 24rpx;
            .textarea-box {
                margin-top: 24rpx;
                width: 100%;
                ::v-deep .u-textarea {
                    background-color: #f7f8fa;
                }
                ::v-deep .u-textarea__count {
                    background-color: #f7f8fa !important;
                }
            }
        }
    }
    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: #fbfbfb;
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
        .btn {
            width: 100%;
            height: 100%;
            background: #fd0100;
            border-radius: 12rpx;
        }
    }
    .passenger-popup {
        padding: 32rpx 24rpx 24rpx 24rpx;
        background: #fff;
        border-radius: 24rpx 24rpx 0 0;
        height: 50vh;
        .search-box {
            padding: 20rpx 0;
        }
    }
}
</style>
