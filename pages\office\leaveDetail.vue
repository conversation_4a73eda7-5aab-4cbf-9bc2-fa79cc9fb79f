<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="请假详情" :isBack="true"></c-navBar>
        </section>
        <section class="status mf-font-32 mf-weight-bold" v-if="info.status">
            <view
                v-for="item in statusList"
                v-if="info.status.value === item.value"
                :key="item.value"
                class="status-item flex flex-center"
                :style="item.style"
            >
                {{ info.status.name }}
            </view>
        </section>
        <section class="info">
            <view class="info-item" v-for="item in info.data" :key="item.label">
                <view class="flex align-center justify-between" v-if="item.type === 'text'">
                    <view class="info-item-label mf-font-28 mf-weight-bold" style="color: #333">{{ item.label }}</view>
                    <view class="info-item-value mf-font-28" style="color: #999">{{ item.value }}</view>
                </view>
                <view class="flex flex-col gap-24" v-if="item.type === 'textarea'">
                    <view class="info-item-label mf-font-28 mf-weight-bold" style="color: #333">{{ item.label }}</view>
                    <view class="info-item-value mf-font-28" style="color: #999">{{ item.value }}</view>
                </view>
                <view class="flex flex-col gap-24" v-if="item.type === 'img'">
                    <view class="info-item-label mf-font-28 mf-weight-bold" style="color: #333">{{ item.label }}</view>
                    <view class="info-item-value mf-font-28 flex flex-wrap gap-24" style="color: #999" v-if="item.value.length > 0">
                        <u-image
                            :src="img"
                            width="200rpx"
                            height="200rpx"
                            radius="8rpx"
                            v-for="(img, index) in item.value"
                            :key="index"
                        ></u-image>
                    </view>
                    <view class="info-item-value mf-font-28" style="color: #999" v-else>--</view>
                </view>
            </view>
        </section>
        <section class="line"></section>
        <section class="progress">
            <view class="progress-title mf-font-32 mf-weight-bold" style="color: #1a1a1a">审批流程</view>
            <view class="steps">
                <view class="step-item flex align-start gap-24" v-for="(item, idx) in approveList" :key="idx">
                    <view class="avatar">
                        <u-image
                            :src="item.approver ? item.approver.avatar : '/static/mine/avatar.png'"
                            width="48rpx"
                            height="48rpx"
                            radius="8rpx"
                            mode="aspectFill"
                        ></u-image>
                    </view>
                    <view class="step-item-content flex flex-col gap-12">
                        <view class="flex align-end step-item-title gap-16">
                            <text class="mf-font-28 mf-weight-bold" style="color: #1a1a1a">
                                {{ item.approver ? item.approver.nickname : "获取失败" }}
                            </text>
                            <text class="mf-font-24" style="color: #666">{{ item.approverRole ? item.approverRole.role_name : "--" }}</text>
                        </view>
                        <view
                            class="mf-font-20"
                            :style="{
                                color:
                                    item.status && item.status.value === 0
                                        ? '#ebb84c'
                                        : item.status && item.status.value === 1
                                        ? '#3198ff'
                                        : item.status && item.status.value === 2
                                        ? '#fd0100'
                                        : '#999',
                            }"
                        >
                            {{ item.status && item.status.name ? item.status.name : "--" }}
                        </view>
                        <view class="step-item-desc mf-font-20" style="color: #999">
                            {{ item.approve_time || "-" }}
                        </view>
                    </view>
                </view>
                <view class="line"></view>
            </view>
        </section>
        <section class="footer flex flex-center" v-if="isSelf && type !== 'leave'">
            <view
                class="btn-item flex-1 flex flex-center"
                style="background: #eeeef0; color: #666666; border-radius: 12rpx 0 0 12rpx"
                @click="confirmRefuseApply"
            >
                <text class="mf-font-36 mf-weight-bold"> 驳回 </text>
            </view>
            <view
                class="btn-item flex-1 flex flex-center"
                style="background: #fd0100; color: #ffffff; border-radius: 0 12rpx 12rpx 0"
                @click="confirmAgreeApply"
            >
                <text class="mf-font-36 mf-weight-bold"> 同意 </text>
            </view>
        </section>
        <u-popup :show="showRefusePopup" mode="center" round="16rpx" :safeAreaInsetBottom="false">
            <view class="refuse-popup">
                <view class="refuse-popup-title mf-font-32 mf-weight-bold">请假驳回</view>
                <view class="refuse-popup-content">
                    <u-textarea
                        v-model="form.refuse_reason"
                        placeholder="请输入驳回原因"
                        :maxlength="80"
                        :auto-height="false"
                        count
                        height="180"
                        :border="false"
                        :custom-style="{ 'background-color': '#f7f7f7' }"
                    ></u-textarea>
                </view>
                <view class="refuse-popup-footer flex">
                    <view class="refuse-popup-btn refuse-popup-btn-cancel" @click="cancelRefuse">取消</view>
                    <view class="refuse-popup-btn refuse-popup-btn-confirm" @click="submitRefuse">提交</view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
export default {
    data() {
        return {
            showRefusePopup: false,
            info: {
                status: {}, // 状态
                data: [], // 数据
            },
            originData: {}, // 原始数据 // 保留做处理
            userInfo: {}, // 用户信息
            approveList: [], // 审批流程
            statusList: [
                { value: 0, style: "background: #fff9ef; color: #ebb84c" },
                { value: 1, style: "background: #e9f4ff; color: #4798f2" },
                { value: 2, style: "background: #ffeeed; color: #fa3231" },
            ],
            form: {
                id: "", // 申请id
                sign_image: "", // 签名图片
                car_id: "", // 车辆id
                refuse_reason: "", // 拒绝原因
            },
        };
    },
    computed: {
        isSelf() {
            if (this.originData && this.originData.approveSelf && this.userInfo) {
                return this.userInfo.id === this.originData.approveSelf.approve_user_id;
            }
        },
    },
    onLoad(options) {
        this.form.id = options.id;
        this.handleGetLeaveInfo(options.id);
        this.userInfo = uni.getStorageSync("userInfo");
    },
    methods: {
        // 图片上传
        handleUploadImage(image, callback) {
            uni.showLoading({
                title: "上传中...",
            });
            uni.uploadFile({
                url: this.vuex_baseUrl + "/common/upload",
                filePath: image,
                name: "file",
                success: (res) => {
                    callback(res);
                },
                fail: (err) => {
                    console.error("图片上传失败", err);
                    this.$fn.showToast("签名图片上传失败");
                },
                complete: () => {
                    uni.hideLoading();
                },
            });
        },
        // 确认同意申请
        confirmAgreeApply() {
            uni.navigateTo({
                url: `/pages/office/sign`,
                events: {
                    // 监听签名完成事件
                    // 当sign.vue页面调用 eventChannel.emit('onSignComplete', ...) 时触发
                    onSignComplete: (data) => {
                        console.log("签名完成，图片路径：", data.signaturePath);
                        // 在这里处理获取到的签名图片路径，例如上传到服务器或在当前页面显示
                        this.handleUploadImage(data.signaturePath, (res) => {
                            const data = JSON.parse(res.data);
                            this.form.sign_image = data.data.url;
                            console.log(this.form.sign_image);

                            uni.showModal({
                                title: "提示",
                                content: "确定同意申请吗？",
                                success: (res) => {
                                    if (res.confirm) {
                                        this.handleAgreeApply();
                                    }
                                },
                            });
                        });
                    },
                },
                fail(err) {
                    console.error("跳转到签名页失败", err);
                },
            });
        },
        // 确认拒绝申请
        confirmRefuseApply() {
            this.showRefusePopup = true;
            console.log(this.showRefusePopup);
        },
        // 取消拒绝申请
        cancelRefuse() {
            this.showRefusePopup = false;
            this.form.refuse_reason = "";
        },
        submitRefuse() {
            if (!this.form.refuse_reason.trim()) {
                return this.$fn.showToast("请输入驳回原因");
            }
            this.handleRefuseApply();
        },
        // 同意申请
        async handleAgreeApply() {
            try {
                const res = await this.$api.agreeApply(this.form);
                if (res.code === 200) {
                    this.$fn.showToast("同意申请成功");
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1000);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("同意申请失败");
            }
        },
        // 拒绝申请
        async handleRefuseApply() {
            try {
                const res = await this.$api.refuseApply({ id: this.form.id, refuse_reason: this.form.refuse_reason });
                if (res.code === 200) {
                    this.$fn.showToast("驳回成功");
                    this.cancelRefuse();
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1000);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("拒绝申请失败");
            }
        },
        // 获取请假详情
        async handleGetLeaveInfo(id) {
            try {
                const res = await this.$api.getLeaveInfo({ id });
                if (res.code === 200) {
                    this.originData = res.data;
                    // 按approve_step从小到大排序
                    this.approveList = (res.data.approve || []).slice().sort((a, b) => {
                        return Number(a.approve_step) - Number(b.approve_step);
                    });
                    this.handleData(res.data);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取请假详情失败");
            }
        },
        // 数据处理
        handleData(data) {
            this.info.status = data.approveSelf.status;
            this.info.data.push({
                label: "请假类别",
                value: data.row.leave_type.name,
                type: "text",
            });
            this.info.data.push({
                label: "申请人",
                value: data.row.user.nickname,
                type: "text",
            });
            this.info.data.push({
                label: "所在单位",
                value: data.row.user.origin_group,
                type: "text",
            });
            this.info.data.push({
                label: "职位",
                value: data.approveSelf.role.role_extend,
                type: "text",
            });
            this.info.data.push({
                label: "开始时间",
                value: data.row.start_date,
                type: "text",
            });
            this.info.data.push({
                label: "结束时间",
                value: data.row.end_date,
                type: "text",
            });
            // 日期处理工具函数
            function toDateOnly(dateStr) {
                const d = new Date(dateStr);
                return new Date(d.getFullYear(), d.getMonth(), d.getDate());
            }
            function diffDays(date1, date2) {
                return Math.max(0, Math.floor((date1 - date2) / (1000 * 60 * 60 * 24)));
            }
            const startDate = toDateOnly(data.row.start_date);
            const endDate = toDateOnly(data.row.end_date);
            const nowDate = toDateOnly(new Date());
            // 请假天数（不包含结束当天）
            const leaveDays = diffDays(endDate, startDate);
            // 剩余天数（结束时间减去当前时间）
            const remainDays = diffDays(endDate, nowDate);
            this.info.data.push({
                label: "请假天数",
                value: `${leaveDays}天`,
                type: "text",
            });
            this.info.data.push({
                label: "剩余天数",
                value: `${remainDays}天`,
                type: "text",
            });
            this.info.data.push({
                label: "到达地点",
                value: data.row.destination,
                type: "textarea",
            });
            this.info.data.push({
                label: "请假事由",
                value: data.row.remarks,
                type: "textarea",
            });
            this.info.data.push({
                label: "图片",
                value: data.row.images,
                type: "img",
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    // background: #f2f4f7;
    padding-bottom: calc(env(safe-area-inset-bottom) + 140rpx);
    .status {
        .status-item {
            padding: 22rpx 0;
        }
    }
    .info {
        padding: 0 24rpx;
        background: #fff;
        .info-item {
            padding: 32rpx 0;
            border-bottom: 1rpx solid #e5e5e5;
            &:last-child {
                border-bottom: none;
            }
        }
    }
    .line {
        height: 10rpx;
        background: #f2f4f7;
    }
    .progress {
        background: #fff;
        padding: 32rpx 24rpx;
        z-index: 888;
        .steps {
            margin-top: 24rpx;
            position: relative;
            .step-item {
                margin-top: 64rpx;
                .avatar {
                    z-index: 777;
                }
                &:first-child {
                    margin-top: 0;
                }
            }
            .line {
                position: absolute;
                top: 0;
                left: 24rpx;
                width: 2rpx;
                height: 90%;
                border-left: 2rpx dashed #c8cacc;
                z-index: 666;
            }
        }
    }
    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: #fff;
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
        z-index: 999;
        .btn-item {
            height: 96rpx;
        }
    }
}
.refuse-popup {
    background-color: #fff;
    width: 624rpx;
    border-radius: 16rpx;
    .refuse-popup-title {
        text-align: center;
        padding: 16rpx 0;
        color: #333;
    }
    .refuse-popup-content {
        padding: 0 32rpx;
    }
    .refuse-popup-counter {
        position: absolute;
        bottom: 10rpx;
        right: 20rpx;
        font-size: 24rpx;
        color: #999;
    }
    .refuse-popup-footer {
        border-top: 1rpx solid #d9d9d9;
        margin-top: 32rpx;
        .refuse-popup-btn {
            flex: 1;
            text-align: center;
            padding: 24rpx 0;
            font-size: 32rpx;
            font-weight: 500;
            color: #fd0100;
        }
        .refuse-popup-btn-cancel {
            border-right: 1rpx solid #d9d9d9;
        }
        .refuse-popup-btn-confirm {
            font-weight: bold;
        }
    }
}
</style>
