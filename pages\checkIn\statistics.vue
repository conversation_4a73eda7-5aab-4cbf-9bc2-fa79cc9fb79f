<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="统计" isTran isPerch isBack> </c-navBar>
        </section>
        <section class="content">
            <uni-calendar
                :class="['calendar', currentDate.date ? 'calendar-active' : '']"
                :showMonth="false"
                :selected="selected"
                @monthSwitch="handleMonthSwitch"
                @change="handleChange"
            />
        </section>
        <section class="dateinfo flex align-center justify-between">
            <view v-if="currentDate.date">{{ currentDate.date }}</view>
            <view class="flex align-center gap-24">
                <view class="flex align-center gap-12">
                    <view class="dot" style="background: #fd0100"></view>
                    <text class="mf-font-28" style="color: #1a1a1a">正常</text>
                </view>
                <view class="flex align-center gap-12">
                    <view class="dot" style="background: #3198ff"></view>
                    <text class="mf-font-28" style="color: #1a1a1a">请假</text>
                </view>
                <view class="flex align-center gap-12">
                    <view class="dot" style="background: #fd7840"></view>
                    <text class="mf-font-28" style="color: #1a1a1a">外出</text>
                </view>
                <!-- <view class="flex align-center gap-12">
                    <view class="dot" style="background: #ebb84c"></view>
                    <text class="mf-font-28" style="color: #1a1a1a">未打卡</text>
                </view> -->
            </view>
        </section>
        <section class="line"></section>
        <section class="status flex flex-col gap-12">
            <view class="status-time flex align-center gap-24" v-if="currentDate">
                <view
                    class="icon mf-font-24 flex flex-center"
                    style="background: #ebb84c"
                    v-if="currentDate.data && currentDate.data.name.includes('未打卡')"
                >
                    <view class="icon-text">领</view>
                </view>
                <view
                    class="icon mf-font-24 flex flex-center"
                    style="background: #3198ff"
                    v-if="currentDate.data && currentDate.data.name.includes('请假')"
                >
                    <view class="icon-text">请</view>
                </view>
                <view
                    class="icon mf-font-24 flex flex-center"
                    style="background: #fd7840"
                    v-if="currentDate.data && currentDate.data.name.includes('外勤打卡')"
                >
                    <view class="icon-text">外</view>
                </view>
                <view
                    class="icon mf-font-24 flex flex-center"
                    style="background: #fd0100"
                    v-if="currentDate.data && currentDate.data.name.includes('正常')"
                >
                    <view class="icon-text">正</view>
                </view>
                <view class="time mf-font-28" style="color: #999999">{{ currentDate.date }}</view>
            </view>
            <view class="status-item flex align-center gap-24" style="margin-left: 68rpx" v-if="currentDate">
                <text class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ currentDate.data ? currentDate.data.name : "" }}</text>
                <view
                    class="status-item-btn flex flex-center"
                    v-if="
                        currentDate.data &&
                        currentDate.data.name == '未打卡' &&
                        new Date(currentDate.date) < new Date(new Date().toDateString())
                    "
                    @click="handleSupplementClock"
                >
                    补领补助
                </view>
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            selected: [], // 选中日期
            originSelected: [], // 原始选中日期(备用)
            startDate: "", // 开始日期
            endDate: "", // 结束日期
            currentDate: {}, // 当前日期
            data: {}, // 补打卡数据
        };
    },
    onShow() {
        const date = new Date();
        this.handleGetDayClockData(this.$u.timeFormat(date.getTime(), "yyyy-mm"));
    },
    onLoad(options) {
        this.data = JSON.parse(options.data);
    },
    methods: {
        // 补打卡
        handleSupplementClock() {
            this.$fn.jumpPage(`/pages/checkIn/supplementClock?data=${JSON.stringify(this.data)}`, true);
        },
        // 选择日期
        handleChange(e) {
            if (this.currentDate === e.extraInfo) return;
            this.currentDate = e.extraInfo;
        },
        // 切换月份
        handleMonthSwitch(e) {
            const month = e.month < 10 ? `0${e.month}` : e.month;
            this.handleGetDayClockData(`${e.year}-${month}`);
        },
        // 获取打卡数据
        async handleGetDayClockData(date) {
            try {
                const res = await this.$api.dayClockData({ month: date });
                if (res.code === 200) {
                    this.originSelected = res.data;
                    this.selected = this.handleData(res.data).filter((item) => item);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取打卡数据失败");
            }
        },
        // 数据处理
        handleData(data) {
            return data.map((item) => {
                // if (item && item.type.name !== "未打卡") {
                let customColor = "#fd0100"; // 默认正常颜色

                // 根据状态设置不同颜色
                if (item.type.name.includes("请假")) {
                    customColor = "#3198ff";
                } else if (item.type.name.includes("外勤打卡")) {
                    customColor = "#fd7840";
                } else if (item.type.name.includes("未打卡")) {
                    customColor = "#ebb84c00";
                } else if (item.type.name.includes("正常")) {
                    customColor = "#fd0100";
                }

                return {
                    date: item.date,
                    info: "",
                    data: {
                        custom: item.type.name,
                        name: item.type.name,
                        color: customColor, // 添加自定义颜色
                    },
                };
                // }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .dateinfo {
        padding: 24rpx;
        .dot {
            width: 12rpx;
            height: 12rpx;
            border-radius: 50%;
        }
    }
    .line {
        width: 100%;
        height: 8rpx;
        background: #f7f7f7;
    }
    .status {
        padding: 24rpx;
        .icon {
            width: 46rpx;
            height: 46rpx;
            border-radius: 50%;
            color: #fff;
            .icon-text {
                width: 100%;
                height: 100%;
                color: #fff;
                text-align: center;
                line-height: 46rpx;
            }
        }
        .status-item {
            height: 80rpx;
            .status-item-btn {
                width: 152rpx;
                height: 52rpx;
                background: #fd0100;
                border-radius: 96rpx;
                color: #fff;
            }
        }
    }
}
</style>
