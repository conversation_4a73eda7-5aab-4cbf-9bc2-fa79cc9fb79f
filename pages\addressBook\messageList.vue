<template>
    <view class="view">
        <section class="groups" style="height: calc(100vh - 405rpx)">
            <scroll-view scroll-y class="scroll-view" style="height: 100%">
                <view
                    class="group-item flex align-center gap-24"
                    v-for="(item, index) in list"
                    :key="index"
                    @click.stop="$fn.jumpPage(`/TUIKit/components/TUIChat/index?conversationID=${item.conversationID}`)"
                >
                    <view class="user flex-1 flex align-center justify-between" v-if="item.userProfile">
                        <view class="flex align-center gap-24">
                            <view class="user-avatar">
                                <u-image
                                    :src="item.userProfile.avatar || '/static/common/group-icon2.png'"
                                    width="128rpx"
                                    height="128rpx"
                                    radius="16rpx"
                                    mode="scaleToFill"
                                ></u-image>
                                <!-- <view class="user-status mf-font-20" style="color: #fff">{{ item.online_status.name }}</view> -->
                            </view>
                            <view class="flex flex-col gap-30">
                                <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">
                                    {{ item.userProfile.nick || "未知用户" }}
                                </view>
                                <view class="mf-font-28 u-line-1" style="color: #ebb84c;width: 400rpx;">{{ item.lastMessage.payload.text }}</view>
                            </view>
                        </view>
                        <view class="action-box">
                            <view
                                class="btn mf-font-28"
                                style="color: #1a1a1a"
                                @click.stop="$fn.jumpPage(`/TUIKit/components/TUIChat/index?conversationID=${item.conversationID}`)"
                            >
                                发私信
                            </view>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </section>
    </view>
</template>

<script>
import TencentCloudChat from "@tencentcloud/chat";
export default {
    data() {
        return {
            list: [],
        };
    },
    onLoad(options) {
        this.getList();
    },
    computed: {},
    methods: {
        async getList() {
            const that = this;
            try {
                

                let promise = uni.$chat.getConversationList({
                    type: TencentCloudChat.TYPES.CONV_C2C,
                });
                promise
                    .then(function (imResponse) {
                        // 全量的会话列表，用该列表覆盖原有的会话列表
                        const conversationList = imResponse.data.conversationList;
                        that.list = conversationList;
                        // 从云端同步会话列表是否完成
                        const isSyncCompleted = imResponse.data.isSyncCompleted;
                        // console.log(conversationList, isSyncCompleted);
                    })
                    .catch(function (imError) {
                        // 获取会话列表失败的相关信息
                        console.warn("getConversationList error:", imError);
                    });
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取会话列表失败");
            }
            // 获取全量的会话列表
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fbfbfb;

    .groups {
        .back-item {
            padding: 30rpx 32rpx;
            background: #fff;
            margin-bottom: 10rpx;
            border-radius: 12rpx;
        }

        .group-item {
            padding: 12rpx 32rpx;
            border-radius: 12rpx;
            background: #fff;
            margin-bottom: 10rpx;
            &:last-child {
                margin-bottom: 0;
            }

            .group {
                display: flex;
                align-items: center;
                gap: 24rpx;
                width: 100%;
            }

            .user {
                .user-avatar {
                    position: relative;
                    .user-status {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        background: #fd0100;
                        border-radius: 16rpx 0rpx 16rpx 0rpx;
                        padding: 4rpx 8rpx;
                    }
                }
                .action-box {
                    .btn {
                        padding: 16rpx 22rpx;
                        border-radius: 8rpx;
                        border: 1rpx solid #e6e6e6;
                    }
                }
            }
        }
    }
}
</style>
