<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="资料库" :isBack="true"> </c-navBar>
        </section>
        <section class="search">
            <view class="search-box">
                <u-input
                    placeholder="搜索"
                    v-model="keywords"
                    @confirm="handleSearch"
                    @blur="handleSearch"
                    border="none"
                    input-align="center"
                />
            </view>
        </section>
        <section class="tabs">
            <u-tabs :list="tabs" activeStyle="color: #FD0100; font-weight: bold;" lineColor="#FD0100" @click="handleClick"></u-tabs>
        </section>
        <section class="list">
            <scroll-view
                scroll-y
                class="scroll-view"
                lower-threshold="100"
                style="height: calc(100vh - 580rpx)"
                @scrolltolower="handleScrollToBottom"
            >
                <view
                    class="list-item flex align-center gap-20"
                    v-for="(item, index) in list"
                    :key="index"
                    @click="$fn.jumpPage(`/pages/office/detail?id=${item.id}`, true)"
                >
                    <!-- 暂不启用 -->
                    <view class="list-item-icon" v-if="false">
                        <u-image
                            v-if="item.type === 'doc'"
                            src="/static/common/doc.png"
                            width="80rpx"
                            height="80rpx"
                            radius="12rpx"
                            mode="aspectFill"
                        ></u-image>
                        <u-image
                            v-if="item.type === 'xls'"
                            src="/static/common/xls.png"
                            width="80rpx"
                            height="80rpx"
                            radius="12rpx"
                            mode="aspectFill"
                        ></u-image>
                        <u-image
                            v-if="item.type === 'pdf'"
                            src="/static/common/pdf.png"
                            width="80rpx"
                            height="80rpx"
                            radius="12rpx"
                            mode="aspectFill"
                        ></u-image>
                        <u-image
                            v-if="item.type === 'ppt'"
                            src="/static/common/ppt.png"
                            width="80rpx"
                            height="80rpx"
                            radius="12rpx"
                            mode="aspectFill"
                        ></u-image>
                    </view>
                    <view class="list-item-title flex flex-col gap-14">
                        <view class="list-item-title-name mf-font-28 mf-weight-bold" style="color: #1a1a1a">{{ item.title }}</view>
                        <view class="list-item-title-time flex align-center gap-20 mf-font-24" style="color: #999">
                            <text>{{ item.create_time }}</text>
                            <view class="line"></view>
                            <text>
                                <text style="color: #fd0100">{{ item.view_user_num }}</text>
                                <text>人查看过</text>
                            </text>
                            <view class="line"></view>
                            <text>{{ item.resource_type.name }}</text>
                        </view>
                    </view>
                </view>
                <u-loadmore :status="status" />
            </scroll-view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            keywords: "",
            current_type: "",
            tabs: [],
            list: [],
            page: 1,
            page_size: 30,
            total_count: 0,
            status: "loadmore",
        };
    },
    onShow() {},
    onLoad(options) {
        this.handleGetResourceList();
    },
    methods: {
        // 点击tab
        handleClick(e) {
            this.page = 1;
            this.list = [];
            this.current_type = e.value;
            this.handleGetResourceList();
        },
        // 滚动到底部
        handleScrollToBottom() {
            if (this.list.length >= this.total_count) {
                this.$fn.showToast("没有更多数据了");
                return;
            }
            this.page++;
            this.handleGetResourceList();
        },
        // 搜索
        handleSearch() {
            this.page = 1;
            this.list = [];
            this.handleGetResourceList();
        },
        // 获取资源列表
        async handleGetResourceList() {
            try {
                if (this.status == "loading") return;
                this.status = "loading";
                const res = await this.$api.getResourceList({
                    resource_type: this.current_type,
                    keywords: this.keywords,
                    page: this.page,
                    page_size: this.page_size,
                });
                if (res.code === 200) {
                    this.tabs = res.data.resource_type;
                    this.list = [...this.list, ...res.data.list];
                    this.total_count = res.data.total_count;
                    if (this.list.length >= this.total_count) this.status = "nomore";
                    else this.status = "loadmore";
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取资源列表失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fbfbfb;
    min-height: 100vh;
    .search {
        padding: 24rpx;
        background: #fff;

        .search-box {
            padding: 20rpx 0;
            border-radius: 12rpx;
            background: #f7f8fa;
        }
    }
    .tabs {
        background: #fff5f5;
    }
    .list {
        padding: 20rpx;
        &-item {
            padding: 20rpx;
            background: #fff;
            // border-radius: 12rpx;
            border-bottom: 1rpx solid #e6e6e6;
            &-title {
                &-time {
                    .line {
                        width: 2rpx;
                        height: 20rpx;
                        background: #e6e6e6;
                    }
                }
            }
        }
    }
}
</style>
