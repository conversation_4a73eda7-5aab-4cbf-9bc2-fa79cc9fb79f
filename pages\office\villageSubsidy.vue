<template>
    <view class="view">
        <section class="bg">
            <image class="bg-img" src="/static/common/office-bg.png" mode="scaleToFill" />
        </section>
        <section class="navbar">
            <c-navBar isTran isPerch title="驻村补助" isBack color="#fff"> </c-navBar>
        </section>
        <section class="user-info">
            <image src="/static/common/user-card.png" mode="widthFix" />
            <view class="user-info-item">
                <view class="flex align-center gap-24">
                    <view class="user-avatar">
                        <u-image
                            :src="userInfo.avatar || '/static/mine/avatar.png'"
                            width="128rpx"
                            height="128rpx"
                            radius="16rpx"
                            mode="scaleToFill "
                        ></u-image>
                        <view class="user-status mf-font-20" style="color: #fff">
                            {{ userInfo.online_status ? userInfo.online_status.name : "获取失败" }}
                        </view>
                    </view>
                    <view class="flex flex-col gap-30">
                        <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ userInfo.nickname }}</view>
                        <view class="mf-font-28" style="color: #ebb84c">{{ userInfo.role ? userInfo.role.role_name : "获取失败" }}</view>
                    </view>
                </view>
            </view>
        </section>

        <section class="statistics">
            <view class="content">
                <view class="mf-font-32 mf-weight-bold flex align-center justify-between" style="color: #1a1a1a">
                    <text>补助统计</text>
                    <view class="flex align-center gap-8" style="color: #999" @click="showDatePicker = true">
                        <text class="mf-font-24">{{ date ? $u.timeFormat(date, "yyyy-mm") : "请选择日期" }}</text>
                        <view :style="{ transform: showDatePicker ? 'rotate(0deg)' : 'rotate(-90deg)', transition: 'transform 0.2s' }">
                            <u-icon name="arrow-down-fill" size="18" color="#999" />
                        </view>
                    </view>
                </view>
                <view class="card-list">
                    <view class="card-item flex align-center justify-between">
                        <view class="flex-1 flex flex-col flex-center gap-36">
                            <text calss="mf-font-28" style="color: #333">每日补助金额</text>
                            <text class="mf-font-48 mf-weight-bold" style="color: #fd0100">
                                {{ villageSubsidyData.day_subsidy_amount || 0 }}元
                            </text>
                        </view>
                        <view class="flex-1 flex flex-col flex-center gap-36">
                            <text calss="mf-font-28" style="color: #333">出勤总天数</text>
                            <text class="mf-font-48 mf-weight-bold" style="color: #fd0100">{{ villageSubsidyData.work_days || 0 }}天</text>
                        </view>
                    </view>
                    <view class="card-item flex align-center justify-between">
                        <view class="flex-1 flex flex-col flex-center gap-36">
                            <text calss="mf-font-28" style="color: #333">
                                {{ villageSubsidyData.subsidy_type ? villageSubsidyData.subsidy_type.tag : "暂无" }}
                            </text>
                            <text class="mf-font-48 mf-weight-bold" style="color: #fd0100">
                                {{ villageSubsidyData.subsidy_type ? villageSubsidyData.subsidy_type.name : "暂无" }}
                            </text>
                        </view>
                        <view class="flex-1 flex flex-col flex-center gap-36">
                            <text calss="mf-font-28" style="color: #333">补助总金额</text>
                            <text class="mf-font-48 mf-weight-bold" style="color: #fd0100">
                                {{ villageSubsidyData.subsidy_amount || 0 }}元
                            </text>
                        </view>
                    </view>
                    <view class="card-item flex align-center justify-between">
                        <view class="flex-1 flex flex-col flex-center gap-36">
                            <text calss="mf-font-28" style="color: #333">休假</text>
                            <text class="mf-font-48 mf-weight-bold" style="color: #fd0100">{{ villageSubsidyData.leave_days || 0 }}天</text>
                        </view>
                        <view class="flex-1 flex flex-col flex-center gap-36">
                            <text calss="mf-font-28" style="color: #333">旷工</text>
                            <text class="mf-font-48 mf-weight-bold" style="color: #fd0100">
                                {{ villageSubsidyData.absences_days || 0 }}天
                            </text>
                        </view>
                    </view>
                    <view class="line one"></view>
                    <view class="line two"></view>
                </view>
            </view>
        </section>
        <section class="footer">
            <view
                class="btn flex flex-center mf-font-36 mf-weight-bold"
                style="color: #fff"
                @click="$fn.jumpPage('/pages/office/distributionRecord', true)"
            >
                发放记录
            </view>
        </section>
        <u-datetime-picker
            :show="showDatePicker"
            v-model="date"
            mode="year-month"
            closeOnClickOverlay
            :maxDate="maxDate"
            @cancel="showDatePicker = false"
            @close="showDatePicker = false"
            @confirm="handleDateConfirm"
        ></u-datetime-picker>
    </view>
</template>

<script>
export default {
    data() {
        return {
            maxDate: new Date().getTime(), // 最大日期
            showDatePicker: false, // 是否显示日期选择器
            date: new Date().getTime(), // 选择日期
            villageSubsidyData: {}, // 驻村补助数据
            userInfo: {}, // 用户信息
            role_view: [], // 角色权限
        };
    },
    onShow() {
        this.handleGetVillageSubsidy();
        this.handleGetMyCenter();
    },
    onPullDownRefresh() {
        this.date = new Date().getTime();
        this.handleGetVillageSubsidy();
        uni.stopPullDownRefresh();
    },
    methods: {
        // 日期选择器确认
        handleDateConfirm(e) {
            // 选择日期后确认逻辑
            this.date = e.value;
            this.showDatePicker = false;
            this.handleGetVillageSubsidy();
        },
        // 获取驻村补助
        async handleGetVillageSubsidy() {
            try {
                const res = await this.$api.villageSubsidy({ date: this.$u.timeFormat(this.date, "yyyy-mm") });
                if (res.code === 200) {
                    this.villageSubsidyData = res.data;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取驻村补助失败");
            }
        },
        // 获取个人中心数据
        async handleGetMyCenter() {
            try {
                const res = await this.$api.getMyCenter();
                if (res.code === 200) {
                    this.userInfo = res.data;
                    this.role_view = res.data.role_view.map((item) => {
                        if (item.role) {
                            return {
                                role_name: item.role.role_name,
                                role_extend: item.role.role_extend,
                            };
                        }
                    });
                    uni.setStorageSync("userInfo", this.userInfo);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取个人中心数据失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fbfbfb;
    min-height: 100vh;

    .bg {
        .bg-img {
            width: 100%;
            height: 600rpx;
        }
    }
    .user-info {
        margin: -580rpx 24rpx 0 24rpx;
        position: relative;
        overflow: hidden;
        height: 192rpx;
        border-radius: 16rpx;
        box-shadow: 0rpx 12rpx 12rpx -4rpx rgba(224, 224, 224, 0.28);
        image {
            width: 100%;
            height: 192rpx;
        }

        .user-info-item {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 32rpx;
            .user-avatar {
                position: relative;
                .user-status {
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    background: #fd0100;
                    border-radius: 16rpx 0rpx 16rpx 0rpx;
                    padding: 4rpx 8rpx;
                }
            }
        }
    }
    .statistics {
        padding: 32rpx 24rpx;
        .content {
            padding: 40rpx 24rpx;
            background: #fff;
            border-radius: 32rpx;
            .card-list {
                margin-top: 34rpx;
                position: relative;
                z-index: 999;
                .card-item {
                    margin-top: 64rpx;
                    background: linear-gradient(180deg, #fff3ea 0%, #ffe8e8 100%);
                    border-radius: 16rpx;
                    padding: 44rpx;
                    &:first-child {
                        margin-top: 0;
                    }
                }
                .line {
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    width: 2rpx;
                    height: 100%;
                    background: #d9d9d9;
                    z-index: -1;
                    &.one {
                        left: 25%;
                    }
                    &.two {
                        right: 25%;
                    }
                }
            }
        }
    }
    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: #fbfbfb;
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
        z-index: 999;
        .btn {
            width: 100%;
            height: 100%;
            background: #fd0100;
            border-radius: 12rpx;
        }
    }
}
</style>
