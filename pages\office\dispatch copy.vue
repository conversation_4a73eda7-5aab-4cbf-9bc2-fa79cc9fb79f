<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="调度" :isBack="true" color="#000"> </c-navBar>
        </section>
        <section class="search">
            <view class="search-box">
                <u-input placeholder="搜索" border="none" input-align="center" />
            </view>
        </section>
        <section class="groups">
            <scroll-view scroll-y class="scroll-view" style="height: calc(100vh - 390rpx)">
                <!-- 市驻村办 -->
                <view
                    class="group-item flex align-center gap-24"
                    @click="
                        $fn.jumpPage(
                            `/pages/office/dispatch-children?data=${JSON.stringify(frameworkData.country_group)}`,
                        )
                    "
                >
                    <u-image src="/static/common/group-icon2.png" width="72rpx" height="72rpx" radius="8rpx" mode="aspectFill"></u-image>
                    <view class="flex align-center justify-between flex-1">
                        <text class="mf-font-32 mf-weight-bold">{{ frameworkData.country_group.framework_label || "加载中..." }}</text>
                        <u-icon name="arrow-right" size="32rpx"></u-icon>
                    </view>
                </view>
                <!-- 派出单位 -->
                <view
                    class="group-item flex align-center gap-24"
                    @click="
                        $fn.jumpPage(`/pages/office/dispatch-children?data=${JSON.stringify(frameworkData.send_group)}`)
                    "
                >
                    <u-image src="/static/common/group-icon2.png" width="72rpx" height="72rpx" radius="8rpx" mode="aspectFill"></u-image>
                    <view class="flex align-center justify-between flex-1">
                        <text class="mf-font-32 mf-weight-bold">{{ frameworkData.send_group.framework_label || "加载中..." }}</text>
                        <u-icon name="arrow-right" size="32rpx"></u-icon>
                    </view>
                </view>
                <!-- 乡镇 -->
                <view
                    class="group-item flex align-center gap-24"
                    v-for="item in frameworkData.town"
                    @click="$fn.jumpPage(`/pages/office/dispatch-children?data=${JSON.stringify(item)}`)"
                >
                    <u-image src="/static/common/group-icon2.png" width="72rpx" height="72rpx" radius="8rpx" mode="aspectFill"></u-image>
                    <view class="flex align-center justify-between flex-1">
                        <text class="mf-font-32 mf-weight-bold">{{ item.town_name || "加载中..." }}</text>
                        <u-icon name="arrow-right" size="32rpx"></u-icon>
                    </view>
                </view>
            </scroll-view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            frameworkData: {},
        };
    },
    onLoad() {
        this.handleGetFrameworkList();
    },
    methods: {
        // 获取组织架构列表
        async handleGetFrameworkList() {
            try {
                const res = await this.$api.getFrameworkList();
                if (res.code === 200) {
                    this.frameworkData = res.data;
                }
            } catch (error) {
                console.log(error);
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fbfbfb;
    min-height: 100vh;
    .search {
        padding: 24rpx;
        background: #fff;

        .search-box {
            padding: 20rpx 0;
            border-radius: 12rpx;
            background: #f7f8fa;
        }
    }
    .groups {
        .group-item {
            padding: 40rpx 32rpx;
            border-radius: 12rpx;
            background: #fff;
            margin-bottom: 10rpx;
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}
</style>
