{"name": "face-verify-callback", "version": "1.0.0", "description": "Receives callback from Tencent Cloud FaceID service.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {}, "cloudfunction-config": {"memorySize": 256, "timeout": 10, "trigger": {"name": "face-verify-callback-trigger", "type": "http", "config": {"authLevel": "anonymous", "functionName": "face-verify-callback", "invokeUrl": "/face-verify-callback", "method": "POST"}}}}