// 引入加密js
import {
	sm4
} from "sm-crypto"
import myFunction from '@/common/js/index.js'
// 响应参数解密的key值，应与后端加密key保持完全一致
const key = "e49a515a1cec7a1cf2340f3abe8f7001"
// 用于跟踪401弹窗是否已显示
let isShowingAuthModal = false;

const toByteArr = (str) => {
	const byteArray = new Uint8Array(str.length / 2)
	for (let i = 0; i < byteArray.length; i++) {
		const index = i * 2
		const byteString = str.substring(index, index + 2)
		byteArray[i] = parseInt(byteString, 16)
	}
	return byteArray
}

// 全局loading唯一请求集合
const loadingSet = new Set();

// 此vm参数为页面的实例，可以通过它引用vuex中的变量
module.exports = (vm) => {
	// 初始化请求配置

	uni.$u.http.setConfig((config) => {
		/* config 为默认全局配置*/
		config.baseURL = vm.vuex_baseUrl
		config.timeout = 50000
		return config
	})

	// 请求拦截
	uni.$u.http.interceptors.request.use((config) => { // 可使用async await 做异步操作
		// 为每个请求生成唯一id
		const loadingId = Symbol('loading');
		config.loadingId = loadingId;
		loadingSet.add(loadingId);
		if (loadingSet.size === 1) {
			uni.showLoading({
				title: '加载中',
				mask: true
			});
		}
		// 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
		// console.log('拦截器',config);
		config.data = config.data || {}
		// 根据custom参数中配置的是否需要token，添加对应的请求头
		// if(config?.custom?.auth) {
		// 可以在此通过vm引用vuex中的变量，具体值在vm.$store.state中
		// console.log("vuex_token", vm.$store.state.vuex_token);

		// 如果已经显示了401弹窗，取消后续请求
		if (isShowingAuthModal) {
			return Promise.reject({
				message: '认证已失效，请先登录'
			});
		}

		config.header = {
			'Authorization': 'Bearer ' + vm.$store.state.vuex_token,
			"login-platform": "MINI_PROGRAM",
		};
		// console.log('拦截器', config);
		if (vm.vuex_encipher) {
			if (config.method.toLowerCase() === "post" || config.method.toLowerCase() === "put") {
				try {
					//加密前请求参数
					console.log("加密前请求参数", config.data)
					// 请求参数加密
					const encryptData = sm4.encrypt(
						JSON.stringify(config.data),
						toByteArr(key)
					)
					config.data = {
						text: encryptData
					}
				} catch (e) {
					//
				}
				const requestObj = {
					url: config.url,
					data: typeof config.data === "object" ?
						JSON.stringify(config.data) : config.data,
					time: new Date().getTime()
				}
			}
		}
		return config
	}, config => { // 可使用async await 做异步操作
		return Promise.reject(config)
	})
	// 响应拦截
	uni.$u.http.interceptors.response.use((response) => {
		// 请求完成移除唯一id
		loadingSet.delete(response.config.loadingId);
		if (loadingSet.size === 0) {
			uni.hideLoading();
		}
		/* 对响应成功做点什么 可使用async await 做异步操作*/
		const data = response.data
		// console.log(response, 'response++++');

		// 自定义参数
		const custom = response.config?.custom

		// 如果没有code，则进行重组返回数据
		if (!data.code) {
			if (response.config.url == '/user/loginByPassword') {
				// 保存token
				console.log("response", response);
				const token = myFunction.getToken(response)
				console.log("token", token);
				vm.$u.vuex("vuex_token", token);
				uni.setStorageSync("token", token);
			}
			return {
				code: response.statusCode,
				data: data.data,
				message: data.message
			}
		}

		if (data.code == 401) {
			uni.clearStorageSync()
			let exists = uni.getStorageSync('token_expired') || false
			// 获取当前打开过的页面路由数组
			let pages = getCurrentPages();
			// 获取当前页面路由，即数组中最后一个元素的路由
			let curRoute = pages[pages.length - 1].route;
			console.log(curRoute);
			let whiteUrl = ['pages/person/person']
			if (!exists && !whiteUrl.includes(curRoute) && !isShowingAuthModal) {
				isShowingAuthModal = true;
				uni.setStorageSync('token_expired', true)
				uni.showModal({
					content: "该模块需要登录,请前往登录后使用完整功能",
					confirmText: "确认",
					success: (res) => {
						isShowingAuthModal = false;
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/tabbar/login'
							})
						} else {
							uni.reLaunch({
								url: '/pages/tabbar/consumerHome'
							})
						}
					}
				})
			}
		} else if (data.code !== 200) {
			console.log(data);
			// 如果没有显式定义custom的toast参数为false的话，默认对报错进行toast弹出提示
			if (custom.toast !== false) {
				uni.$u.toast(data.message)
			}

			// 如果需要catch返回，则进行reject
			if (custom?.catch) {
				return Promise.reject(data)
			} else {
				// 否则返回一个pending中的promise，请求不会进入catch中
				return new Promise(() => { })
			}
		}
		if (vm.vuex_encipher) {
			try {
				if (typeof data.data === "string") {
					let decryptedData = sm4.decrypt(
						toByteArr(data.data),
						toByteArr(key)
					)
					if (
						decryptedData.charAt(0) === "[" ||
						decryptedData.charAt(0) === "{"
					) {
						decryptedData = JSON.parse(decryptedData)
					}
					//解密响应参数
					console.log("解密响应参数", decryptedData)
					return {
						...data,
						data: decryptedData
					}
				}
			} catch (e) {
				return data
			}
		}
		return data
		// return data.data === undefined ? {} : data.data
	}, (response) => {
		// 请求异常也要移除唯一id
		if (response.config && response.config.loadingId) {
			loadingSet.delete(response.config.loadingId);
			if (loadingSet.size === 0) {
				uni.hideLoading();
			}
		}
		if (response.statusCode == 401) {
			uni.clearStorageSync()
			// 获取当前打开过的页面路由数组
			let pages = getCurrentPages();
			// 获取当前页面路由，即数组中最后一个元素的路由
			let curRoute = pages[pages.length - 1].route;
			console.log(curRoute);
			let whiteUrl = ['pages/person/person']
			if (!whiteUrl.includes(curRoute) && !isShowingAuthModal) {
				isShowingAuthModal = true;
				uni.showModal({
					content: "该模块需要登录,请前往登录后使用完整功能",
					confirmText: "确认",
					success: (res) => {
						isShowingAuthModal = false;
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/tabbar/login'
							})
						} else {
							uni.reLaunch({
								url: '/pages/tabbar/consumerHome'
							})
						}
					}
				})
			}
		}
		if (response.statusCode == 502) {
			uni.showModal({
				title: '警告',
				content: '服务出现异常，请联系管理员处理',
				showCancel: true,
				success: ({ confirm, cancel }) => { }
			})
		}
		if (!isShowingAuthModal) {
			uni.$u.toast(response.data.message)
		}
		return response.data
		// return Promise.reject(response)
	})
}