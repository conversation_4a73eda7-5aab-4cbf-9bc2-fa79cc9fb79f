<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="资料详情" :isBack="true" showRight>
                <view slot="right" class="collection" @click="handleCollectResource">
                    <u-icon name="star" size="24" color="#999999" v-if="!isCollection"></u-icon>
                    <u-icon name="star-fill" size="24" color="#EBB84C" v-else></u-icon>
                </view>
            </c-navBar>
        </section>
        <section class="status flex align-center justify-between mf-font-24" style="color: #999999">
            <view class="status-item flex align-center gap-10">
                <text>{{ data.create_time }}</text>
                <view class="line"></view>
                <text>{{ data.resource_type ? data.resource_type.name : "" }}</text>
            </view>
            <view class="status-item flex align-center gap-10">
                <text>
                    <text style="color: #fd0100">{{ data.view_user_num || 0 }}</text>
                    <text>人已读</text>
                </text>
                <view class="line"></view>
                <view class="status-item-unread" @click="$fn.jumpPage(`/pages/office/unreadList?id=${data.id}`)">
                    {{ data.no_view_user_num || 0 }}人未读
                </view>
            </view>
        </section>
        <section class="content">
            <view class="content-title mf-font-32 font-weight-bold">{{ data.title }}</view>
            <u-parse :content="data.content"></u-parse>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            isCollection: false,
            data: {},
        };
    },
    onLoad(options) {
        this.handleGetResourceDetail(options.id);
    },
    methods: {
        handleCollection() {
            this.isCollection = !this.isCollection;
        },
        // 获取资源详情
        async handleGetResourceDetail(id) {
            try {
                const res = await this.$api.getResourceDetail({ resource_id: id });
                if (res.code === 200) {
                    this.data = res.data;
                    this.isCollection = res.data.is_collect == 0 ? false : true;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取资源详情失败");
            }
        },
        // 收藏/取消收藏资源
        async handleCollectResource() {
            try {
                const res = await this.$api.collectResource({ resource_id: this.data.id });
                if (res.code === 200) {
                    this.isCollection = !this.isCollection;
                    this.$fn.showToast(this.isCollection ? "收藏成功" : "取消收藏成功");
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("收藏/取消收藏资源失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    min-height: 100vh;

    .status {
        padding: 24rpx;
        border-bottom: 2rpx solid #e5e5e5;

        .status-item {
            .line {
                width: 2rpx;
                height: 24rpx;
                background: #999999;
            }

            .status-item-unread {
                padding: 4rpx 16rpx;
                border-radius: 8rpx;
                border: 2rpx solid #fd0100;
                color: #fd0100;
            }
        }
    }

    .content {
        padding: 24rpx;
    }
}
</style>
