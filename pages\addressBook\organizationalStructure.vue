<template>
    <view class="view">
        <section class="groups">
            <scroll-view scroll-y class="scroll-view" style="height: calc(100vh - 390rpx)">
                <view class="group-item" v-for="(item, index) in list" :key="index">
                    <!-- 分组头部 -->
                    <view class="group-header flex align-center justify-between" @click="toggleGroup(index)">
                        <view class="flex align-center gap-20">
                            <u-image src="/static/common/group-icon2.png" width="72rpx" height="72rpx" radius="8rpx" mode="aspectFill"></u-image>
                            <text class="mf-font-32 mf-weight-bold">{{ item.group_name }}</text>
                        </view>
                        <u-icon :name="item.open ? 'arrow-down' : 'arrow-right'" size="32rpx"></u-icon>
                    </view>
                    <!-- 展开内容 -->
                    <view v-if="item.open" class="expand">
                        <!-- 有user直接渲染用户 -->
                        <view v-if="item.user" class="users flex flex-col gap-16" style="margin-left: 16rpx">
                            <view class="user flex-1 flex align-center justify-between" v-for="user in item.user" :key="index + '-' + user.id" @click="handleCheck(user)">
                                <view class="flex align-center gap-24">
                                    <view class="user-avatar">
                                        <u-image :src="user.avatar || '/static/common/group-icon2.png'" width="100rpx" height="100rpx" radius="16rpx" mode="scaleToFill "></u-image>
                                        <view class="user-status mf-font-20" style="color: #fff">{{ user.online_status.name }}</view>
                                    </view>
                                    <view class="flex flex-col gap-30">
                                        <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ user.nickname }}</view>
                                        <view class="mf-font-28" style="color: #ebb84c">{{ user.role_name || "驻村队长" }}</view>
                                    </view>
                                </view>
                                <view class="action-box">
                                    <view class="btn mf-font-28" style="color: #1a1a1a" @click.stop="handleClickMessage(user)">
                                        发私信
                                    </view>
                                </view>
                            </view>
                        </view>
                        <!-- 有list则渲染下一级分组 -->
                        <view v-if="item.list" class="list flex flex-col gap-16" style="margin-left: 16rpx">
                            <view class="sub-group" v-for="(sub, subIdx) in item.list" :key="subIdx">
                                <view class="sub-group-header flex align-center justify-between" @click="toggleSubGroup(index, subIdx)">
                                    <view class="flex align-center gap-20">
                                        <u-image src="/static/common/group-icon2.png" width="72rpx" height="72rpx" radius="8rpx" mode="aspectFill"></u-image>
                                        <text class="mf-font-32 mf-weight-bold">{{ sub.group_name }}</text>
                                    </view>
                                    <u-icon :name="sub.open ? 'arrow-down' : 'arrow-right'" size="28rpx"></u-icon>
                                </view>
                                <view v-if="sub.open" class="flex flex-col gap-16" style="margin-left: 16rpx">
                                    <view class="user flex-1 flex align-center justify-between" v-for="user in sub.user" :key="index + '-' + subIdx + '-' + user.id" @click="handleCheck(user)">
                                        <view class="flex align-center gap-24">
                                            <view class="user-avatar">
                                                <u-image :src="user.avatar || '/static/common/group-icon2.png'" width="100rpx" height="100rpx" radius="16rpx" mode="scaleToFill "></u-image>
                                                <view class="user-status mf-font-20" style="color: #fff">
                                                    {{ user.online_status.name }}
                                                </view>
                                            </view>
                                            <view class="flex flex-col gap-30">
                                                <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ user.nickname }}</view>
                                                <view class="mf-font-28" style="color: #ebb84c">{{ user.role_name || "驻村队长" }}</view>
                                            </view>
                                        </view>
                                        <view class="action-box">
                                            <view class="btn mf-font-28" style="color: #1a1a1a" @click.stop="handleClickMessage(user)">
                                                发私信
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </section>
    </view>
</template>

<script>
export default {
    name: "organizationalStructure",
    data() {
        return {
            frameworkData: {},
            list: [],
        };
    },
    methods: {
        // 获取组织架构列表
        async handleGetFrameworkList() {
            try {
                const res = await this.$api.getFrameworkList();
                if (res.code === 200) {
                    this.frameworkData = res.data;
                    this.$emit("frameworkDataNum", res.data.village_total_user);
                    this.list = await this.dataCollation(res.data);
                }
            } catch (error) {
                console.log(error);
            }
        },

        // 数据整理
        async dataCollation(data) {
            let netData = [];
            // 遍历所有数据
            for (const [key, value] of Object.entries(data)) {
                // 处理市级和派驻组数据
                if (typeof value === "object" && !Array.isArray(value) && value !== null) {
                    if (value.user?.length > 0) {
                        netData.push({
                            group_name: value.framework_label,
                            user: value.user,
                            open: false,
                        });
                    }
                }

                // 处理乡镇数据（乡镇下有村，村下才是用户）
                if (Array.isArray(value)) {
                    value.forEach((ele) => {
                        netData.push({
                            group_name: ele.town_name,
                            list: ele.role_group.map((e) => {
                                return {
                                    group_name: e.framework_label,
                                    user: e.user,
                                    open: false,
                                };
                            }),
                            open: false,
                        });
                    });
                }
            }
            return netData;
        },
        // 手风琴切换
        toggleGroup(index) {
            this.list[index].open = !this.list[index].open;
        },
        toggleSubGroup(parentIdx, subIdx) {
            this.list[parentIdx].list[subIdx].open = !this.list[parentIdx].list[subIdx].open;
        },
        // 发送私信
        async handleClickMessage(item) {
            // #ifndef APP-PLUS
            this.$fn.showToast("请前往APP使用");
            return;
            // #endif
            try {
                // 切换到 public001 群会话
                console.log(uni.$chat);
                const promise = await uni.$chat.getConversationProfile(`C2C${item.id}`);
                console.log("会话查找成功", promise);
                this.$fn.jumpPage(`/TUIKit/components/TUIChat/index?conversationID=C2C${item.id}`);
            } catch (error) {
                console.error("切换群会话失败", error);
                
                uni.showModal({
                    title: "切换群会话失败",
                    content: `对方未登陆过该系统或未上线：${error.message}`,
                    showCancel: false,
                    confirmText: "确定",
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .groups {
        .group-item {
            padding: 20rpx;
            // border-radius: 12rpx;
            background: #fff;
            margin-bottom: 10rpx;

            &:last-child {
                margin-bottom: 0;
            }

            .user {
                &:first-child {
                    margin-top: 16rpx;
                }

                .user-avatar {
                    position: relative;

                    .user-status {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        background: #fd0100;
                        border-radius: 16rpx 0rpx 16rpx 0rpx;
                        padding: 4rpx 8rpx;
                    }
                }

                .action-box {
                    .btn {
                        padding: 16rpx 22rpx;
                        border-radius: 8rpx;
                        border: 1rpx solid #e6e6e6;
                    }
                }
            }

            .expand {
                .list {
                    margin-top: 16rpx;
                }
            }
        }
    }
}
</style>
