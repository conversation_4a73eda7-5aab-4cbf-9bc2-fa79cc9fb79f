<template>
    <!-- 更换手机号-设置新手机号 -->
    <view class="container">
        <c-navBar title="更换手机号" isSeat isPerch :isBack="true"></c-navBar>

        <view class="content">
            <view class="title">修改密码</view>

            <!-- <view class="phone-info"> 请先输入你的旧密码再输入新密码 </view> -->

            <view class="form-container">
                <view class="input-item">
                    <view class="input-label">新密码</view>
                    <input type="text" placeholder="请输入新密码" v-model="form.password" />
                </view>

                <view class="input-item">
                    <view class="input-label">确认密码</view>
                    <input type="text" placeholder="请再次输入新密码" v-model="form.confirm_password" />
                    <!-- <view class="code-btn" @click="getVerifyCode">{{ isCounting ? `${countdown}s` : '发送验证码' }}</view> -->
                </view>
            </view>
            <view class="btn-box">
                <button class="save-btn" @click="handleForgetPassword">保存</button>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            form: {
                code: "", // 验证码
                mobile: "", // 手机号
                password: "", // 新密码
                confirm_password: "", // 确认密码
            },
        };
    },
    onLoad(options) {
        this.form.code = options.code;
        this.form.mobile = options.mobile;
    },
    methods: {
        async handleForgetPassword() {
            try {
                const res = await this.$api.forgetPassword(this.form);
                if (res.code === 200) {
                    this.$fn.showToast("修改密码成功");
                    uni.reLaunch({
                        url: "/pages/tabbar/login",
                    });
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("修改密码时出错");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    background-color: #fff;
}

.header {
    height: 100rpx;
    display: flex;
    align-items: center;
    position: relative;

    .back-icon {
        position: absolute;
        left: 30rpx;

        image {
            width: 40rpx;
            height: 40rpx;
        }
    }

    .header-title {
        width: 100%;
        text-align: center;
        font-size: 36rpx;
        font-weight: bold;
    }
}

.content {
    padding: 32rpx;
}

.title {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 24rpx;
}

.phone-info {
    font-size: 30rpx;
    color: #666;
    margin-bottom: 44rpx;
}

.form-container {
    margin-bottom: 80rpx;
}

.input-item {
    height: 100rpx;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    position: relative;

    .input-label {
        width: 160rpx;
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
    }

    input {
        flex: 1;
        height: 100%;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
    }

    .code-btn {
        color: #ff0036;
        font-size: 28rpx;
        white-space: nowrap;
    }
}

.btn-box {
    position: fixed;
    padding: 20rpx 32rpx 68rpx 32rpx;
    background: #fff;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    z-index: 999;
}
.save-btn {
    width: 686rpx;
    height: 96rpx;
    background: linear-gradient(315deg, #ec3015 0%, #ff0036 100%);
    border-radius: 12rpx;
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 500;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
