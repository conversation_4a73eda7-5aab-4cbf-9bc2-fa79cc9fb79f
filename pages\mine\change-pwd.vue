<template>
    <!-- 更换手机号-设置新手机号 -->
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="忘记密码" isSeat isPerch :isBack="true"></c-navBar>
        </section>

        <section class="content">
            <view class="title mf-font-32 mf-weight-bold">设置密码</view>
            <view class="phone-info mf-font-24"> 请先输入你的旧密码再输入新密码 </view>

            <view class="form-container">
                <view class="input-item flex align-center">
                    <view class="input-label">原密码</view>
                    <input type="text" placeholder="请输入原密码" v-model="oldPhone" />
                </view>
                <view class="input-item flex align-center">
                    <view class="input-label">新密码</view>
                    <input type="text" placeholder="请输入新密码" v-model="newPhone" />
                </view>

                <view class="input-item flex align-center">
                    <view class="input-label">确认密码</view>
                    <input type="text" placeholder="请再次输入新密码" v-model="verifyCode" />
                </view>
            </view>
        </section>
        <section class="footer">
            <view class="btn flex flex-center mf-font-36 mf-weight-bold" style="color: #fff" @click="handleSubmit">保存</view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            newPhone: "",
            verifyCode: "",
            countdown: 60,
            isCounting: false,
        };
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },

        // 获取验证码
        getVerifyCode() {
            if (this.isCounting) return;

            if (!this.newPhone) {
                uni.showToast({
                    title: "请输入新手机号",
                    icon: "none",
                });
                return;
            }

            // 此处调用获取验证码接口
            uni.showToast({
                title: "验证码已发送",
                icon: "none",
            });

            // 开始倒计时
            this.isCounting = true;
            this.countdown = 60;
            const timer = setInterval(() => {
                this.countdown--;
                if (this.countdown <= 0) {
                    this.isCounting = false;
                    clearInterval(timer);
                }
            }, 1000);
        },

        // 保存新手机号
        saveNewPhone() {
            if (!this.newPhone) {
                uni.showToast({
                    title: "请输入新手机号",
                    icon: "none",
                });
                return;
            }

            if (!this.verifyCode) {
                uni.showToast({
                    title: "请输入验证码",
                    icon: "none",
                });
                return;
            }

            // 此处调用保存新手机号接口
            uni.showToast({
                title: "手机号更换成功",
                icon: "none",
                success: () => {
                    setTimeout(() => {
                        uni.navigateBack({
                            delta: 2, // 返回上上级页面
                        });
                    }, 1500);
                },
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    min-height: 100vh;
    background-color: #fff;
    .content {
        padding: 32rpx;
        .title {
            color: #1a1a1a;
            margin-bottom: 24rpx;
        }
        .phone-info {
            color: #fd0100;
            margin-bottom: 44rpx;
        }
        .form-container {
            margin-bottom: 80rpx;

            .input-item {
                padding: 24rpx 0;
                border-bottom: 1px solid #eee;
                position: relative;

                .input-label {
                    width: 160rpx;
                    font-weight: 500;
                    font-size: 28rpx;
                    color: #333333;
                }

                input {
                    flex: 1;
                    height: 100%;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #999999;
                }

                .code-btn {
                    color: #ff0036;
                    font-size: 28rpx;
                    white-space: nowrap;
                }
            }
        }
    }
    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: #fbfbfb;
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
        .btn {
            width: 100%;
            height: 100%;
            background: #fd0100;
            border-radius: 12rpx;
        }
    }
}
</style>
