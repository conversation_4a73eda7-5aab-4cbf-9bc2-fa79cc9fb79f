<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="车辆派遣" isBack></c-navBar>
        </section>
        <section class="list" v-if="list.length > 0">
            <scroll-view scroll-y style="height: calc(100vh - 300rpx)">
                <view class="list-item flex align-start justify-between" v-for="item in list" :key="item.id" @click="
                    $fn.jumpPage(`/pages/office/applyDetail?id=${item.apply.id}&type=${item.apply.type ? item.apply.type.name : ''}&dispatch=${dispatch}`)
                    ">
                    <view class="left flex flex-col gap-16">
                        <view class="flex align-center gap-22" v-if="item.apply">
                            <text calss="mf-font-32 mf-weight-bold" style="color: #333">
                                {{ item.apply.nickname }}-{{ item.apply.type ? item.apply.type.name : "" }}
                            </text>
                            <view class="status mf-font-20" style="color: #fff">
                                {{ item.status ? item.status.name : "" }}
                            </view>
                        </view>
                        <view class="flex flex-col gap-8 mf-font-24" style="color: #999999" v-if="item.apply">
                            <text>开始时间：{{ item.apply.start_date }}</text>
                            <text>结束时间：{{ item.apply.end_date }}</text>
                            <text>申请人：{{ item.apply.nickname }}</text>
                            <text>申请时间：{{ item.apply.create_time }}</text>
                        </view>
                    </view>
                    <view class="right">
                        <u-image src="/static/common/status1.png" v-if="item.status.value === 1" width="120rpx" height="120rpx" mode="aspectFill"></u-image>
                        <u-image src="/static/common/status2.png" v-if="item.status.value === 2" width="120rpx" height="120rpx" mode="aspectFill"></u-image>
                        <u-image src="/static/common/status3.png" v-if="item.status.value === 3" width="120rpx" height="120rpx" mode="aspectFill"></u-image>
                        <u-image src="/static/common/status4.png" v-if="item.status.value === 0" width="120rpx" height="120rpx" mode="aspectFill"></u-image>
                    </view>
                </view>
            </scroll-view>
        </section>
        <section class="empty flex flex-center" v-else>
            <u-empty text="暂无记录" />
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            approve_status: 1, // 0:待审批 1:已同意 2:已驳回 3:已查阅
            list: [], // 待审批列表
            dispatch: false, // 是否是车辆派遣
            userInfo: {}, // 用户信息
        };
    },
    onLoad() {
        this.handleGetMyApply(this.approve_status);
        this.handleGetMyCenter();
    },
    methods: {
        // 获取待审批数据
        async handleGetMyApply(approve_status) {
            try {
                const data = {
                    approve_status, // 审批状态
                    type: 2, // 类型 (这里后端做了类型2和4的一起返回，所以只传2)
                    is_all: 1 // 显示当前审核人员所在乡镇的全部
                }
                const res = await this.$api.myApply(data);
                if (res.code === 200) {
                    this.list = res.data.list;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取待审批数据失败");
            }
        },
        // 获取个人中心数据
        async handleGetMyCenter() {
            try {
                const res = await this.$api.getMyCenter();
                if (res.code === 200) {
                    this.userInfo = res.data;
                    this.dispatch = res.data.role_view.some((item) => item.role.role_name == "驻村办干事");
                    uni.setStorageSync("userInfo", this.userInfo);
                }
            } catch (error) {
                console.log(error);
                this.$fn.showToast("获取个人中心数据失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .navbar {
        background: #fff;
    }

    .list {
        padding: 24rpx;
        background-color: #fbfbfb;

        .list-item {
            padding: 24rpx 20rpx;
            background: #ffffff;
            border-radius: 12rpx;
            margin-top: 24rpx;

            &:first-child {
                margin-top: 0;
            }

            .left {
                .status {
                    padding: 4rpx 10rpx;
                    background: #fd0100;
                    border-radius: 12rpx 0rpx 12rpx 0rpx;
                }
            }
        }
    }
}
</style>
