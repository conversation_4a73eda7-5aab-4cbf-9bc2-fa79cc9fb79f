<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar isPerch title="设置" isBack> </c-navBar>
        </section>
        <section class="settings">
            <view
                class="action flex align-center justify-between"
                @click="$fn.jumpPage(`/pages/tabbar/change-pwd?phoneNumber=${userInfo.mobile}`, true)"
            >
                <text class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">修改密码</text>
                <u-icon name="arrow-right" size="20" color="#666"></u-icon>
            </view>
            <view class="action flex align-center justify-between" @click="$fn.jumpPage(`/pages/mine/agreement?title=关于我们&type=about_us`, false)">
                <text class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">关于我们</text>
                <u-icon name="arrow-right" size="20" color="#666"></u-icon>
            </view>
            <view class="action flex align-center justify-between" @click="$fn.jumpPage(`/pages/mine/agreement?title=用户协议&type=customer_service_agreement`, false)">
                <text class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">用户协议</text>
                <u-icon name="arrow-right" size="20" color="#666"></u-icon>
            </view>
            <view class="action flex align-center justify-between" @click="$fn.jumpPage(`/pages/mine/agreement?title=隐私协议&type=customer_privacy_agreement`, false)">
                <text class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">隐私协议</text>
                <u-icon name="arrow-right" size="20" color="#666"></u-icon>
            </view>
            <view class="action flex align-center justify-between" @click="checkUpdate">
                <view class="flex align-center justify-between" style="color: #1a1a1a; width: 100%">
                    <view class="mf-font-32 mf-weight-bold">检测更新</view>
                    <view class="mf-font-24 mf-weight-bold" style="color: #999" v-if="current_version">v{{ current_version }}</view>
                </view>
                <u-icon name="arrow-right" size="20" color="#666"></u-icon>
            </view>
            <view class="action flex align-center justify-between" @click="clearCache">
                <text class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">清理缓存</text>
                <u-icon name="arrow-right" size="20" color="#666"></u-icon>
            </view>
            <view class="action flex align-center justify-between" @click="confirmDestroy">
                <text class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">注销账号</text>
                <u-icon name="arrow-right" size="20" color="#666"></u-icon>
            </view>
        </section>
        <section class="footer">
            <view class="btn flex flex-center mf-font-36 mf-weight-bold" style="color: #fff" @click="confirmLogout">退出登录</view>
        </section>
        <u-popup :show="show" @close="close" mode="center" round="16rpx">
            <view class="update-popup">
                <view class="update-title">发现新版本</view>
                <view class="update-version">v{{ latestAppInfo.version_name }}</view>
                <scroll-view scroll-y class="update-content">
                    <rich-text :nodes="latestAppInfo.content"></rich-text>
                </scroll-view>
                <view class="update-actions">
                    <view class="action-btn cancel" @click="close">稍后更新</view>
                    <view class="action-btn confirm" @click="updateApp">立即更新</view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
export default {
    data() {
        return {
            userInfo: {},
            current_version: "", // 当前版本号 ps:v1.0.0
            current_version_number: 0, // 当前版本号 ps:100
            latestAppInfo: {}, // 云端版本信息
            show: false,
        };
    },
    onLoad() {
        this.userInfo = uni.getStorageSync("userInfo");
        // 获取当前版本号
        const systemInfo = uni.getAppBaseInfo();
        this.current_version = systemInfo.appVersion;
        this.current_version_number = systemInfo.appVersionCode;
    },
    methods: {
        // 清理缓存
        clearCache() {
            uni.showModal({
                title: "提示",
                content: "清理缓存会清除所有缓存数据，包括登录信息、缓存图片等，并回到登陆页面。",
                success: (res) => {
                    if (res.confirm) {
                        uni.clearStorageSync();
                        this.$fn.showToast("清理缓存成功");
                        setTimeout(() => {
                            uni.reLaunch({ url: "/pages/tabbar/login" });
                        }, 1000);
                    }
                },
            });
        },
        close() {
            this.show = false;
        },
        // 更新app
        updateApp() {
            // #ifdef APP-PLUS
            if (this.latestAppInfo.apk_url) {
                plus.runtime.openURL(this.latestAppInfo.apk_url, (res) => {
                    console.log(res);
                });
            } else {
                this.$fn.showToast("获取新版本下载链接失败");
            }
            // #endif
        },
        // 检测更新
        async checkUpdate() {
            try {
                // #ifdef APP-PLUS
                const res = await this.$api.getLatestAppVersion();
                if (res.code == 200) {
                    this.latestAppInfo = {
                        ...res.data,
                        content: res.data.content.replace(/\n/g, "<br>"),
                        version_name: String(res.data.version).split("").join("."),
                    };
                    if (this.current_version_number < this.latestAppInfo.version) {
                        this.show = true;
                    } else {
                        this.$fn.showToast("当前已是最新版本");
                    }
                }
                // #endif
                // #ifndef APP-PLUS
                this.$fn.showToast("请在APP端使用");
                // #endif
            } catch (error) {
                console.error(error);
                this.$fn.showToast("检测更新时出错");
            }
        },
        // 确认退出登录
        confirmLogout() {
            uni.showModal({
                title: "提示",
                content: "确定退出登录吗？",
                success: (res) => {
                    if (res.confirm) {
                        this.handleLogout();
                    }
                },
            });
        },
        // 确认注销用户
        confirmDestroy() {
            uni.showModal({
                title: "提示",
                content: "确定注销用户吗？注销后无法再次登录。",
                success: (res) => {
                    if (res.confirm) {
                        this.handleDestroy();
                    }
                },
            });
        },
        // 退出登录
        async handleLogout() {
            try {
                const res = await this.$api.logout();
                if (res.code == 200) {
                    this.$fn.showToast("退出登录成功");
                    // #ifdef APP-PLUS
                    uni.$TUICallKit.logout((res) => {
                        if (res.code === 0) {
                            console.log("音视频退出成功", res);
                        } else {
                            console.error(res);
                        }
                    });
                    uni.$TUILogin.logout((res) => {
                        if (res.code === 0) {
                            console.log("IM退出成功", res);
                        } else {
                            console.error(res);
                        }
                    });
                    // #endif
                    setTimeout(() => {
                        uni.clearStorage();
                        uni.reLaunch({ url: "/pages/tabbar/login" });
                    }, 1000);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("退出登录时出错");
            }
        },
        // 注销用户
        async handleDestroy() {
            try {
                const res = await this.$api.destroy();
                if (res.code == 200) {
                    this.$fn.showToast("注销用户成功");
                    setTimeout(() => {
                        uni.clearStorage();
                        uni.reLaunch({ url: "/pages/tabbar/login" });
                    }, 1000);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("注销用户时出错");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .settings {
        padding: 24rpx;
        .action {
            padding: 32rpx 0;
            border-bottom: 2rpx solid #e6e6e6;
        }
    }
    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: #fbfbfb;
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
        .btn {
            width: 100%;
            height: 100%;
            background: #fd0100;
            border-radius: 12rpx;
        }
    }
    .update-popup {
        width: 600rpx;
        padding: 40rpx;
        box-sizing: border-box;
        .update-title {
            font-size: 36rpx;
            font-weight: bold;
            text-align: center;
            color: #333;
        }
        .update-version {
            font-size: 30rpx;
            text-align: center;
            color: #666;
            margin-top: 16rpx;
        }
        .update-content {
            margin-top: 40rpx;
            max-height: 300rpx;
            color: #666;
            font-size: 28rpx;
            line-height: 1.6;
        }
        .update-actions {
            margin-top: 50rpx;
            display: flex;
            justify-content: space-between;
            .action-btn {
                width: 240rpx;
                height: 80rpx;
                line-height: 80rpx;
                text-align: center;
                border-radius: 40rpx;
                font-size: 30rpx;
                font-weight: bold;
            }
            .cancel {
                background-color: #f0f0f0;
                color: #666;
            }
            .confirm {
                background-color: #fd0100;
                color: #fff;
            }
        }
    }
}
</style>
