// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作，更多内容详见uView对拦截器的介绍部分：
// https://uviewui.com/js/http.html#%E4%BD%95%E8%B0%93%E8%AF%B7%E6%B1%82%E6%8B%A6%E6%88%AA%EF%BC%9F

// 引入分工api.js 
import dApi from "./divide/d-api.js"
import qApi from "./divide/q-api.js"


const http = uni.$u.http
const getApi = (params = {}, config) => http.get('/frontend/user/login', { params, ...config });
const postApi = (data = {}, config) => http.post('/frontend/yard/redeemRecord', data, config);

const logout = (params = {}, config) => http.get('/user/logout', { params, ...config })

const sendCode = (data = {}, config) => http.post('/user/sendCode', data, config);
const loginByPassword = (data = {}, config) => http.post('/user/loginByPassword', data, config);
const getHomepage = (data = {}, config) => http.post('/user/homepage', data, config);
const getMyCenter = (data = {}, config) => http.post('/user/myCenter', data, config);
const getFrameworkList = (data = {}, config) => http.post('/framework/list', data, config);
const getResourceList = (data = {}, config) => http.post('/resource/list', data, config);
const getResourceDetail = (data = {}, config) => http.post('/resource/detail', data, config);
const collectResource = (data = {}, config) => http.post('/resource/collect', data, config);
const getNoReadUserList = (data = {}, config) => http.post('/resource/noReadUserList', data, config);
const getCollectList = (data = {}, config) => http.post('/resource/collectList', data, config);
const searchUser = (data = {}, config) => http.post('/user/searchUser', data, config);
const getLeaveData = (data = {}, config) => http.post('/user/getLeaveData', data, config);
const getReportUserList = (data = {}, config) => http.post('/user/recordUserList', data, config);
const getUseList = (data = {}, config) => http.post('/car/useList', data, config);
const getAllDriver = (data = {}, config) => http.post('/dataview/allDriver', data, config);
const getUserList = (data = {}, config) => http.post('/user/searchUser', data, config);
const urgentSentCar = (data = {}, config) => http.post('/car/urgentSentCar ', data, config);
const getCarList = (data = {}, config) => http.post('/dataview/carlist', data, config);
const leaveApplication = (data = {}, config) => http.post('/user/askForLeave', data, config);
const leaveApplicationOther = (data = {}, config) => http.post('/user/askForLeaveOther', data, config);
const leaveUserList = (data = {}, config) => http.post('/v1/user/leaveUserList', data, config);
const getLeaveInfo = (data = {}, config) => http.post('/dataview/leaveInfo', data, config);
const destroy = (data = {}, config) => http.post('/user/destroy', data, config);
const forgetPassword = (data = {}, config) => http.post('/user/forgetPassword', data, config);
const clockCenter = (data = {}, config) => http.post('/user/clockCenter', data, config);
const villageSubsidy = (data = {}, config) => http.post('/user/villageSubsidy', data, config);
const clock = (data = {}, config) => http.post('/user/clock', data, config);
const outClock = (data = {}, config) => http.post('/user/outClock', data, config);
const reClock = (data = {}, config) => http.post('/user/reClock', data, config);
const getSystemConfig = (data = {}, config) => http.post('/common/getSystemConfig', data, config);
const feedback = (data = {}, config) => http.post('/user/feedback', data, config);
const userSentLog = (data = {}, config) => http.post('/user/userSubsidySentLog', data, config);
const agreeApply = (data = {}, config) => http.post('/apply/agree', data, config);
const refuseApply = (data = {}, config) => http.post('/apply/refuse', data, config);
const fundsStatistics = (data = {}, config) => http.post('/apply/fundsStatistics', data, config);
const preApplyFundsData = (data = {}, config) => http.post('/apply/preApplyFundsData', data, config);
const preApplyFunds = (data = {}, config) => http.post('/apply/preApplyFunds', data, config);
const fundsApply = (data = {}, config) => http.post('/apply/fundsApply', data, config);
const outReport = (data = {}, config) => http.post('/apply/outReport', data, config);
const getOutReportData = (data = {}, config) => http.post('/apply/getOutReportData', data, config);
const pendingApproval = (data = {}, config) => http.post('/apply/pendingApproval', data, config);
const myApply = (data = {}, config) => http.post('/user/myApply', data, config);
const getClockSetting = (data = {}, config) => http.post('/user/getClockSetting', data, config);
const setClockTip = (data = {}, config) => http.post('/user/setClockTip', data, config);
const setClockAuto = (data = {}, config) => http.post('/user/setClockAuto', data, config);
const dayClockData = (data = {}, config) => http.post('/user/dayClockData', data, config);
const leaveDetail = (data = {}, config) => http.post('/apply/leaveDetail', data, config);
const reclockDetail = (data = {}, config) => http.post('/apply/reclockDetail', data, config);
const outReportDetail = (data = {}, config) => http.post('/apply/outReportDetail', data, config);
const fundsDetail = (data = {}, config) => http.post('/apply/fundsDetail', data, config);
const setCar = (data = {}, config) => http.post('/apply/setCar', data, config);
const getLatestAppVersion = (params = {}, config) => http.get('/common/getLatestAppVersion', { params, ...config });
const handleCar = (data = {}, config) => http.post('/car/handle', data, config);
const clearLeave = (data = {}, config) => http.post('/apply/clearLeave', data, config);
const continueLeave = (data = {}, config) => http.post('/apply/continueLeave', data, config);
let apiList = {
	...dApi,
	...qApi,
	getApi, //get接口示例
	postApi, //post接口示例

	logout, // 退出登录

	sendCode, // 发送验证码
	loginByPassword, // 密码登录
	getHomepage, // 获取首页数据
	getMyCenter, // 获取个人中心数据
	getFrameworkList, // 获取组织架构列表
	getResourceList, // 获取资源列表
	getResourceDetail, // 获取资源详情
	collectResource, // 收藏/取消收藏资源
	getNoReadUserList, // 获取未读用户列表
	getCollectList, // 获取收藏列表
	searchUser, // 搜索用户
	getLeaveData, // 获取请假相关数据
	getReportUserList, // 获取报备列表
	getUseList, // 获取用车列表
	getAllDriver, // 获取所有司机
	getUserList, // 搜索用户
	urgentSentCar, // 紧急派车
	getCarList, // 获取车辆列表
	leaveApplication, // 请假申请
	leaveApplicationOther, // 代他人请假
	leaveUserList, // 获取请假列表
	getLeaveInfo, // 获取请假详情
	destroy, // 注销用户
	forgetPassword, // 忘记密码
	clockCenter, // 领补助中心(领补助页面那4个数据)
	villageSubsidy, // 获取驻村补助
	userSentLog, // 获取用户补助记录
	clock, // 领补助
	outClock, // 外勤领补助
	reClock, // 补领补助
	getSystemConfig, // 获取系统配置
	feedback, // 举报投诉/意见反馈
	agreeApply, // 同意申请
	refuseApply, // 拒绝申请
	preApplyFunds, // 资金绩效
	fundsStatistics, // 资金绩效统计
	preApplyFundsData, // 经费类别
	fundsApply, // 经费申请
	outReport, // 外出报备
	getOutReportData, // 获取外出报备数据
	pendingApproval, // 获取待审批数据
	myApply, // 获取我的审批列表
	getClockSetting, // 获取领补助提醒/自动领补助设置状态
	setClockTip, // 设置领补助提醒
	setClockAuto, // 设置自动领补助
	dayClockData, // 获取领补助数据
	leaveDetail, // 获取请假详情
	reclockDetail, // 获取补领补助详情
	outReportDetail, // 获取外出详情
	fundsDetail, // 获取资金详情
	setCar, // 分配司机
	getLatestAppVersion, // 获取最新版本
	handleCar, // 处理车辆
	clearLeave, // 销假
	continueLeave, // 续假
}
export default { ...apiList }