<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar isPerch title="收藏资料" isBack> </c-navBar>
        </section>
        <section class="list" v-if="list.length > 0">
            <!-- <c-scroll-list :api="api" :apiParams="apiParams" :options="{ auto: false }" @load="load"> -->
            <view
                class="list-item flex align-center gap-20"
                v-for="(item, index) in list"
                :key="index"
                @click="$fn.jumpPage(`/pages/office/detail?id=${item.resource_id}`, true)"
            >
                <!-- 暂不启用 -->
                <view class="list-item-icon" v-if="false">
                    <u-image
                        v-if="item.type === 'doc'"
                        src="/static/common/doc.png"
                        width="80rpx"
                        height="80rpx"
                        radius="12rpx"
                        mode="aspectFill"
                    ></u-image>
                    <u-image
                        v-if="item.type === 'xls'"
                        src="/static/common/xls.png"
                        width="80rpx"
                        height="80rpx"
                        radius="12rpx"
                        mode="aspectFill"
                    ></u-image>
                    <u-image
                        v-if="item.type === 'pdf'"
                        src="/static/common/pdf.png"
                        width="80rpx"
                        height="80rpx"
                        radius="12rpx"
                        mode="aspectFill"
                    ></u-image>
                    <u-image
                        v-if="item.type === 'ppt'"
                        src="/static/common/ppt.png"
                        width="80rpx"
                        height="80rpx"
                        radius="12rpx"
                        mode="aspectFill"
                    ></u-image>
                </view>
                <view class="list-item-title flex flex-col gap-14">
                    <view class="list-item-title-name mf-font-28 mf-weight-bold" style="color: #1a1a1a">{{ item.resource.title }}</view>
                    <view class="list-item-title-time flex align-center gap-20 mf-font-24" style="color: #999">
                        <text>{{ item.create_time }}</text>
                        <view class="line"></view>
                        <text>
                            <text style="color: #fd0100">{{ item.resource.view_user_num }}</text>
                            <text>人查看过</text>
                        </text>
                        <view class="line"></view>
                        <text>{{ item.resource.type_text }}</text>
                    </view>
                </view>
            </view>
            <u-loadmore :status="status" />
            <!-- </c-scroll-list> -->
        </section>
        <section class="empty flex flex-center" style="height: 300rpx" v-else>
            <u-empty mode="favor" icon="favor" />
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            list: [],
            page: 1,
            pageSize: 10,
            has_more: true,
            status: "loadmore",
        };
    },
    onLoad() {
        this.handleGetCollectList();
    },
    onReachBottom() {
        if (this.has_more) {
            this.page++;
            this.handleGetCollectList();
        }
    },
    methods: {
        async handleGetCollectList() {
            try {
                if (this.status == "loading") return;
                this.status = "loading";
                const res = await this.$api.getCollectList({ current_page: this.page, page_size: this.pageSize });
                if (res.code === 200) {
                    this.list = [...this.list, ...res.data.list];
                    this.has_more = res.data.has_more == 0 ? true : false;
                    if (!this.has_more) this.status = "nomore";
                    else this.status = "loadmore";
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取收藏列表失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .list {
        padding: 20rpx;
        &-item {
            padding: 20rpx;
            background: #fff;
            // border-radius: 12rpx;
            border-bottom: 1rpx solid #e6e6e6;
            &-title {
                &-time {
                    .line {
                        width: 2rpx;
                        height: 20rpx;
                        background: #e6e6e6;
                    }
                }
            }
        }
    }
}
</style>
