<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar isPerch title="举报投诉" isBack> </c-navBar>
        </section>
        <section class="content">
            <u--form
                labelPosition="left"
                :model="form"
                :rules="rules"
                ref="uForm"
                labelWidth="220rpx"
                :labelStyle="{ color: '#1a1a1a', fontWeight: 'bold' }"
                errorType="toast"
            >
                <view class="form">
                    <u-form-item label="联系电话" prop="mobile" borderBottom>
                        <u-input
                            v-model="form.mobile"
                            inputAlign="right"
                            type="number"
                            border="none"
                            maxlength="11"
                            :suffixIconStyle="{ fontSize: '24rpx' }"
                            placeholder="请输入联系电话"
                        />
                    </u-form-item>
                    <u-form-item label="举报内容" prop="content" labelPosition="top">
                        <view class="textarea-box">
                            <u--textarea
                                v-model="form.content"
                                placeholderStyle="color: #999"
                                count
                                border="none"
                                height="300rpx"
                                maxlength="80"
                                placeholder="请输入举报内容"
                            ></u--textarea>
                        </view>
                    </u-form-item>
                    <u-form-item label="图片上传" prop="content" labelPosition="top">
                        <view class="upload-box">
                            <c-upLoadImgs :maxCount="3" @update:file="handleUpdateFile"></c-upLoadImgs>
                            <!-- <c-upload-cloud :maxCount="3" @update:file="handleUpdateFile"></c-upload-cloud> -->
                        </view>
                    </u-form-item>
                </view>
            </u--form>
        </section>
        <section class="footer">
            <view class="btn flex flex-center mf-font-36 mf-weight-bold" style="color: #fff" @click="handleSubmit">提交</view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            form: {
                mobile: "",
                content: "",
                images: [],
            },
            rules: {
                mobile: [
                    { required: true, type: "number", max: 11, message: "请输入联系电话" },
                    {
                        // 自定义验证函数，见上说明
                        validator: (rule, value, callback) => {
                            // 上面有说，返回true表示校验通过，返回false表示不通过
                            // uni.$u.test.mobile()就是返回true或者false的
                            return uni.$u.test.mobile(value);
                        },
                        message: "手机号码不正确",
                        // 触发器可以同时用blur和change
                        trigger: ["change", "blur"],
                    },
                ],
                content: [{ required: true, type: "string", max: 80, message: "请输入举报内容" }],
            },
        };
    },
    onReady() {
        this.$refs.uForm.setRules(this.rules);
    },
    methods: {
        handleUpdateFile(file) {
            this.form.images = file;
        },
        handleSubmit() {
            this.$refs.uForm.validate().then(async (res) => {
                if (res) {
                    try {
                        const res = await this.$api.feedback(this.form);
                        if (res.code === 200) {
                            this.$fn.showToast("提交成功");
                            setTimeout(() => {
                                this.$fn.jumpBack();
                            }, 1000);
                        }
                    } catch (error) {
                        console.error(error);
                        this.$fn.showToast("提交失败");
                    }
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        padding-bottom: 164rpx;
        .form {
            padding: 24rpx 24rpx 0 24rpx;
            background-color: #fff;
            border-radius: 24rpx;
            .textarea-box {
                margin-top: 24rpx;
                width: 100%;
                ::v-deep .u-textarea {
                    background-color: #f7f8fa;
                }
                ::v-deep .u-textarea__count {
                    background-color: #f7f8fa !important;
                }
            }
            .upload-box {
                margin-top: 24rpx;
            }
        }
    }
    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: #fbfbfb;
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
        .btn {
            width: 100%;
            height: 100%;
            background: #fd0100;
            border-radius: 12rpx;
        }
    }
}
</style>
