<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar title="设置" isTran isPerch isBack> </c-navBar>
        </section>
        <section class="settings">
            <view class="settings-item flex align-center justify-between">
                <view class="flex flex-col gap-16">
                    <text class="mf-font-28 mf-weight-bold" style="color: #1a1a1a">补助领取提醒</text>
                    <text class="mf-font-24" style="color: #999999">自动发送领补助提醒</text>
                </view>
                <u-switch v-model="clockTipSwitch" active-color="#fd0100" inactive-color="#999999"></u-switch>
            </view>
            <view class="settings-item flex align-center justify-between" style="opacity: 0.5">
                <view class="flex flex-col gap-16">
                    <text class="mf-font-28 mf-weight-bold" style="color: #1a1a1a">自动补助领取</text>
                    <text class="mf-font-24" style="color: #999999">到时间自动完成补助领取</text>
                </view>
                <u-switch disabled v-model="autoClockSwitch" active-color="#fd0100" inactive-color="#999999"></u-switch>
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            is_clock_tip: 0, // 补助领取提醒
            is_auto_clock: 0, // 自动补助领取
        };
    },
    computed: {
        clockTipSwitch: {
            get() {
                return this.is_clock_tip === 1;
            },
            set(val) {
                const value = val ? 1 : 0;
                if (this.is_clock_tip === value) return;
                this.is_clock_tip = value;
                this.handleSetClockSetting("is_clock_tip", value);
            },
        },
        autoClockSwitch: {
            get() {
                return this.is_auto_clock === 1;
            },
            set(val) {
                const value = val ? 1 : 0;
                if (this.is_auto_clock === value) return;
                this.is_auto_clock = value;
                this.handleSetClockSetting("is_auto_clock", value);
            },
        },
    },
    onLoad(options) {
        this.handleGetClockSetting();
    },
    methods: {
        // 获取领补助提醒/自动领补助设置状态
        async handleGetClockSetting() {
            try {
                const res = await this.$api.getClockSetting();
                if (res.code === 200) {
                    this.is_clock_tip = res.data.is_clock_tip;
                    this.is_auto_clock = res.data.is_auto_clock;
                }
            } catch (error) {
                console.error("获取领补助设置失败：", error);
                this.$fn.showToast("获取领补助设置出错");
            }
        },

        // 设置领补助提醒/自动领补助
        async handleSetClockSetting(key, value) {
            try {
                let res;
                // 领补助提醒
                if (key === "is_clock_tip") {
                    res = await this.$api.setClockTip({ status: value });
                }
                // 自动领补助
                if (key === "is_auto_clock") {
                    res = await this.$api.setClockAuto({ status: value });
                }
                if (res.code === 200) {
                    this.$fn.showToast("设置成功");
                } else {
                    this.$fn.showToast(res.msg || "设置失败");
                    this.handleGetClockSetting(); // 如果失败，则恢复为服务器端的状态
                }
            } catch (error) {
                console.error("更新领补助设置失败：", error);
                this.$fn.showToast("更新领补助设置出错");
                this.handleGetClockSetting(); // 如果异常，则恢复为服务器端的状态
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fbfbfb;
    min-height: 100vh;
    .settings {
        padding: 24rpx 32rpx;
        .settings-item {
            background: #fff;
            padding: 32rpx;
            border-radius: 16rpx;
            margin-top: 24rpx;
            &:first-child {
                margin-top: 0;
            }
        }
    }
}
</style>
