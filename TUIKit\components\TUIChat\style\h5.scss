.chat {
  display: block;
  height: 100%;
  overflow: hidden;
}

.tui-chat-h5 {
  &-message-list {
    flex: 1;
    overflow: hidden;
    display: flex;
    margin-bottom: 80px; /* 为底部输入区域预留空间 */
    transition: margin-bottom 0.3s ease;
  }

  &-message-input {
    height: auto;
    padding: 10px 10px 0;
    border-top: 1px solid #eee;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    z-index: 1001;
    transition: bottom 0.3s ease;
  }

  &-message-input-toolbar {
    order: 1;
    border: none;
    position: fixed;
    bottom: -220px; /* 默认隐藏在输入框下方 */
    left: 0;
    right: 0;
    background: white;
    z-index: 1000;
    border-top: 1px solid #eee;
    height: 220px;
    transition: bottom 0.3s ease;
    
    /* 当工具栏显示时 */
    &.show {
      bottom: 0;
    }
  }
}

/* H5平台工具栏展开状态 */
.tui-chat-h5.toolbar-expanded {
  .tui-chat-h5-message-input {
    bottom: 220px;
  }
  
  .tui-chat-h5-message-list {
    margin-bottom: 300px; /* 输入框80px + 工具栏220px */
  }
}

.tui-chat-uniapp {
  &-header {
    display: none;
  }
}

.group-profile {
  position: absolute;
  top: 14%;
  right: 0;
  width: 50px;
  height: 30px;
  line-height: 30px;
  color: #000;
  font-size: 10px;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  padding-left: 15px;
  z-index: 100;
  background-color: #ccc;
  opacity: 0.5;
}
