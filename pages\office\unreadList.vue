<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="未读人列表" :isBack="true"></c-navBar>
        </section>
        <section class="list">
            <scroll-view scroll-y class="scroll-view">
                <view class="group-item flex align-center gap-24" v-for="item in list" :key="item.id">
                    <view class="user flex-1 flex align-center justify-between">
                        <view class="flex align-center gap-24">
                            <view class="user-avatar">
                                <u-image
                                    :src="item.avatar || '/static/common/avatar.png'"
                                    width="128rpx"
                                    height="128rpx"
                                    radius="16rpx"
                                    mode="scaleToFill "
                                ></u-image>
                                <!-- <view class="user-status mf-font-20" style="color: #fff">{{ item.status }}</view> -->
                            </view>
                            <view class="flex flex-col gap-30">
                                <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ item.nickname }}</view>
                                <view class="mf-font-28" style="color: #ebb84c">{{ item.role.role_name }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            list: [],
        };
    },
    onLoad(options) {
        this.handleGetNoReadUserList(options.id);
    },
    methods: {
        async handleGetNoReadUserList(id) {
            try {
                const res = await this.$api.getNoReadUserList({ resource_id: id });
                if (res.code === 200) {
                    this.list = res.data.list;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取未读用户列表失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .list {
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
        .group-item {
            padding: 26rpx 0;
            background: #fff;
            border-bottom: 2rpx solid #e5e5e5;
            .user {
                .user-avatar {
                    position: relative;
                    .user-status {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        background: #fd0100;
                        border-radius: 16rpx 0rpx 16rpx 0rpx;
                        padding: 4rpx 8rpx;
                    }
                }
            }
        }
    }
}
</style>
