<template>
    <view class="view">
        <section class="nav-bar">
            <c-navBar isPerch title="个人主页" isBack> </c-navBar>
        </section>
        <section class="user-info flex flex-center flex-col">
            <view class="user-avatar">
                <u-image v-if="userInfo.avatar" :src="userInfo.avatar" width="172rpx" height="172rpx" radius="16rpx" mode="aspectFill"></u-image>
                <u-image v-else src="/static/mine/avatar.png" width="172rpx" height="172rpx" radius="16rpx" mode="aspectFill"></u-image>
                <view class="user-status mf-font-20" style="color: #fff">在岗</view>
            </view>
            <view class="user-name mf-font-32 mf-weight-bold">{{ userInfo.nickname }}</view>
        </section>
        <section class="line"></section>
        <section class="user-info-list">
            <view class="user-info-item flex align-center justify-between" v-if="userInfo.role">
                <text class="mf-font-28 mf-weight-bold" style="color: #1a1a1a">驻村职务</text>
                <text class="mf-font-28" style="color: #666666">{{ userInfo.role.role_name }}</text>
            </view>
            <view class="user-info-item flex align-center justify-between" v-if="userInfo.role">
                <text class="mf-font-28 mf-weight-bold" style="color: #1a1a1a">单位职务</text>
                <text class="mf-font-28" style="color: #666666">{{ userInfo.role.framework_label }}</text>
            </view>
            <view class="user-info-item flex align-center justify-between">
                <text class="mf-font-28 mf-weight-bold" style="color: #1a1a1a">驻村位置</text>
                <text class="mf-font-28" style="color: #666666">{{ userInfo.group_name }}</text>
            </view>
            <view class="user-info-item flex align-center justify-between">
                <text class="mf-font-28 mf-weight-bold" style="color: #1a1a1a">电话</text>
                <text class="mf-font-28" style="color: #666666">{{ userInfo.mobile }}</text>
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            userInfo: {}, // 用户信息
            role_view: [], // 角色权限
        };
    },
    onShow() {
        this.handleGetMyCenter();
    },
    methods: {
        // 获取个人中心数据
        async handleGetMyCenter() {
            try {
                const res = await this.$api.getMyCenter();
                if (res.code === 200) {
                    this.userInfo = res.data;
                    this.role_view = res.data.role_view.map((item) => {
                        if (item.role) {
                            return {
                                role_name: item.role.role_name,
                                role_extend: item.role.role_extend,
                            };
                        }
                    });
                }
            } catch (error) {
                console.log(error);
                this.$fn.showToast("获取个人中心数据失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .user-info {
        padding: 40rpx 0;
        .user-avatar {
            width: 172rpx;
            height: 172rpx;
            position: relative;
            .user-status {
                position: absolute;
                bottom: 0;
                right: 0;
                background: #fd0100;
                border-radius: 16rpx 0rpx 16rpx 0rpx;
                padding: 6rpx 12rpx;
            }
        }
        .user-name {
            margin-top: 24rpx;
            color: #1a1a1a;
            text-align: center;
        }
    }
    .line {
        width: 100%;
        height: 8rpx;
        background: #f7f7f7;
    }
    .user-info-list {
        padding: 24rpx;
        .user-info-item {
            padding: 34rpx 0;
            border-bottom: 2rpx solid #e6e6e6;
        }
    }
}
</style>
