<template>
    <view class="info">
        <section class="nav-bar">
            <c-navBar title="个人信息" isPerch :isBack="true"> </c-navBar>
        </section>
        <section class="content">
            <view @click="uploadAvatar" class="item">
                <text class="text">头像</text>
                <view class="item-right">
                    <image class="head" v-if="form.avatar" :src="vuex_imgUrl + form.avatar" mode="aspectFill"></image>
                    <image class="head" v-else src="/static/mine/avatar.png"></image>
                    <u-icon name="arrow-right" color="#666666" size="20rpx"></u-icon>
                </view>
            </view>

            <view class="items item">
                <text class="text">昵称</text>
                <view class="item-right">
                    <input class="ipt" v-model="form.nickname" placeholder="请输入" />
                </view>
            </view>
            <view class="items item" @click="$fn.jumpPage('/pages/tabbar/change-phone', false)">
                <text class="text">手机号码</text>
                <view class="item-right">
                    <input class="ipt" disabled v-model="form.phone" placeholder="请输入手机号" />
                </view>
            </view>
        </section>

        <!-- 按钮 -->
        <section class="footer">
            <view @click="submit" class="btn-box">
                <view class="btn mf-font-32 mf-weight-bold">保存</view>
            </view>
        </section>
    </view>
</template>

<script>
import permision from "@/utils/permission.js";
export default {
    data() {
        return {
            form: {
                avatar: "", //头像
                nickname: "", //昵称
                phone: "", //手机号
            },
        };
    },
    onLoad(option) {
        const systemInfo = uni.getSystemInfoSync();
        // 判断操作系统
        if (systemInfo.platform === "android") {
            console.log("运行在Android上");
            plus.navigator.setFullscreen(true); // 隐藏状态栏
        } else if (systemInfo.platform === "ios") {
            console.log("运行在iOS上");
        } else {
            console.log("运行在开发者工具上");
        }
        // this.getUserInfoFn();
    },
    onShow() {
        console.log(uni.getStorageSync("avatar"), "头像");
        if (uni.getStorageSync("avatar")) {
            this.form.avatar = uni.getStorageSync("avatar");
            console.log(uni.getStorageSync("avatar"));
            uni.removeStorageSync("avatar");
        }
    },
    methods: {
        getUserInfoFn() {
            this.$api.getUserInfo().then((res) => {
                console.log(res, "用户信息");
                this.form = res.data;
            });
        },
        selectSex() {
            this.show = true;
        },
        selectedSex(list) {
            // console.log(list);
            this.form.sex = list.value[0].type;
            this.show = false;
        },
        // 上传头像
        async uploadAvatar() {
            // uni.chooseImage({
            //   count: 1, //默认9
            //   sizeType: ["compressed"], // 可以指定是原图还是压缩图，默认二者都有
            //   sourceType: ["album", "camera"], // 可以指定来源是相册还是相机，默认二者都有
            //   success: (res) => {
            //     const src = res.tempFilePaths[0];
            //     uni.navigateTo({
            //       url: "./herderPicture?src=" + src,
            //     });
            //   },
            //   fail: (res) => {
            //     console.log(res);
            //   },
            // });
            var result = await permision.premissionCheck("CAMERA");
            var result2 = await permision.premissionCheck("EXTERNAL_STORAGE");
            if (result == 1 || result2 == 1) {
                uni.chooseImage({
                    count: 1, //默认9
                    sizeType: ["compressed"], // 可以指定是原图还是压缩图，默认二者都有
                    sourceType: ["album", "camera"], // 可以指定来源是相册还是相机，默认二者都有
                    success: (res) => {
                        const src = res.tempFilePaths[0];
                        uni.navigateTo({
                            url: "./herderPicture?src=" + src,
                        });
                    },
                    fail: (res) => {
                        console.log(res);
                    },
                });
            }
        },
        getInfo() {
            this.$api.userInfo().then((res) => {
                this.form = res.data;
                this.form.phone = this.$fn.phoneEn(res.data.phone);
            });
        },
        submit() {
            if (this.form.name == "") {
                uni.showToast({
                    mask: true,
                    icon: "none",
                    title: "昵称不能为空",
                });
                return;
            }

            this.$api.setUser(this.form).then((res) => {
                uni.showToast({
                    icon: "none",
                    title: "操作成功",
                    success() {
                        setTimeout(() => uni.navigateBack(), 1000);
                    },
                });
            });
            this.$api.getUserInfo().then((res) => {
                console.log(res, "修改后的用户信息");
                this.form = res.data;
                uni.setStorageSync("userInfo", res.data);
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.info {
    .content {
        padding: 40rpx 30rpx;
        .item {
            display: flex;
            justify-content: space-between;
            border-bottom: 2rpx solid #eeeeee;
            padding: 34rpx 0;
            align-items: center;
            .head {
                width: 90rpx;
                height: 90rpx;
                border-radius: 45rpx;
                margin-right: 10rpx;
            }
        }
    }
}

.item-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.text {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
}

.ipt {
    width: 180rpx;
    font-size: 24rpx;
    color: #999999;
    margin-right: 8rpx;
    text-align: right;
}

.ipt1 {
    width: 500rpx;
}

::v-deep .u-textarea {
    background-color: #f7f7f7 !important;
}

.footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 0 40rpx;
    --iphonex-fix-bottom: env(safe-area-inset-bottom);
    padding-bottom: var(--iphonex-fix-bottom);

    .btn-box {
        height: 96rpx;
        text-align: center  ;
        line-height: 96rpx;
        background: linear-gradient(315deg, #ec3015 0%, #ff0036 100%);
        border-radius: 48rpx;
        color: #fff;
    }
}
</style>
