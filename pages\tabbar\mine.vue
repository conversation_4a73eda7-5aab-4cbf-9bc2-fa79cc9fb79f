<template>
    <view class="view">
        <section class="home-bg">
            <image src="/static/common/mine-bg.png" mode="scaleToFill" />
        </section>
        <section class="navbar">
            <c-navBar isTran isPerch :isBack="false" title="个人中心" color="#fff" showRight>
                <view slot="right" class="setting-btn" @click="$fn.jumpPage('/pages/mine/settings', false)">
                    <u-image src="/static/common/mine-setting.png" width="40rpx" height="40rpx" mode="aspectFill"></u-image>
                </view>
            </c-navBar>
        </section>
        <section class="user-info flex align-center gap-24" @click="$fn.jumpPage('/pages/mine/information', true)">
            <view class="user-avatar">
                <u-image
                    :src="userInfo.avatar || '/static/mine/avatar.png'"
                    width="172rpx"
                    height="172rpx"
                    radius="16rpx"
                    mode="aspectFill"
                ></u-image>
                <view class="user-status mf-font-20" style="color: #fff">
                    {{ userInfo.online_status ? userInfo.online_status.name : "获取失败" }}
                </view>
            </view>
            <view class="flex flex-col" style="color: #fff">
                <view class="flex align-end gap-12">
                    <text class="mf-font-32 mf-weight-bold">{{ userInfo.nickname }}</text>
                    <text class="mf-font-28">{{ userInfo.role ? userInfo.role.role_name : "获取失败" }}</text>
                    <text class="mf-font-24">{{ userInfo.birthday ? userInfo.birthday.age : "获取失败" }}岁</text>
                </view>
                <view class="flex mf-font-24 align-center gap-12" style="margin-top: 30rpx">
                    <text>单位职务:</text>
                    <text>{{ userInfo.role ? userInfo.role.role_name : "获取失败" }}</text>
                </view>
                <view class="flex mf-font-24 align-center gap-12" style="margin-top: 16rpx">
                    <text>直属领导:</text>
                    <text>{{ userInfo.direct_leader }}</text>
                </view>
            </view>
        </section>
        <section class="data-info">
            <view class="card flex align-center justify-between">
                <view class="data-item flex flex-center flex-col gap-34">
                    <text class="mf-font-36 mf-weight-bold" style="color: #fd0100">{{ userInfo.allowance || 0 }}</text>
                    <text class="mf-font-24 mf-weight-bold" style="color: #1a1a1a">补助金(元)</text>
                </view>
                <view class="line"></view>
                <view class="data-item flex flex-center flex-col gap-34">
                    <text class="mf-font-36 mf-weight-bold" style="color: #fd0100">
                        {{ userInfo.year_out_days || 0 }}/{{ userInfo.year_leave_out_days || 0 }}/{{ userInfo.leave_year_days || 0 }}
                    </text>
                    <text class="mf-font-24 mf-weight-bold" style="color: #1a1a1a">外勤天数/请假天数/剩余天数</text>
                </view>
                <view class="line"></view>
                <view class="data-item flex flex-center flex-col gap-34">
                    <text class="mf-font-36 mf-weight-bold" style="color: #fd0100">{{ userInfo.record_days || 0 }}</text>
                    <text class="mf-font-24 mf-weight-bold" style="color: #1a1a1a">报备次数</text>
                </view>
            </view>
        </section>
        <section class="actions-list">
            <view
                class="action-item flex align-center gap-16"
                v-for="(item, index) in actions"
                :key="index"
                @click="$fn.jumpPage(item.path, item.isLogin)"
            >
                <u-image :src="item.icon" width="28rpx" height="28rpx"></u-image>
                <view class="flex flex-1 align-center justify-between">
                    <text class="mf-font-28 mf-weight-bold" style="color: #333">{{ item.title }}</text>
                    <u-icon name="arrow-right" size="18" color="#999999"></u-icon>
                </view>
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            userInfo: {}, // 用户信息
            role_view: [], // 角色权限
            actions: [
                {
                    icon: "/static/common/mine1.png",
                    title: "收藏资料",
                    path: "/pages/mine/collectionMaterials",
                    isLogin: true,
                },
                {
                    icon: "/static/common/mine2.png",
                    title: "举报投诉",
                    path: "/pages/mine/report",
                    isLogin: true,
                },
                {
                    icon: "/static/common/mine3.png",
                    title: "用户协议",
                    path: `/pages/mine/agreement?title=用户协议&type=customer_service_agreement`,
                    isLogin: false,
                },
                {
                    icon: "/static/common/mine4.png",
                    title: "隐私协议",
                    path: `/pages/mine/agreement?title=隐私协议&type=customer_privacy_agreement`,
                    isLogin: false,
                },
                {
                    icon: "/static/common/mine5.png",
                    title: "关于我们",
                    path: `/pages/mine/agreement?title=关于我们&type=about_us`,
                    isLogin: false,
                },
            ],
        };
    },
    onShow() {
        this.handleGetMyCenter();
    },
    methods: {
        // 获取个人中心数据
        async handleGetMyCenter() {
            try {
                const res = await this.$api.getMyCenter();
                if (res.code === 200) {
                    this.userInfo = res.data;
                    this.role_view = res.data.role_view.map((item) => {
                        if (item.role) {
                            return {
                                role_name: item.role.role_name,
                                role_extend: item.role.role_extend,
                            };
                        }
                    });
                }
            } catch (error) {
                console.log(error);
                this.$fn.showToast("获取个人中心数据失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    position: relative;
    min-height: 100vh;

    .home-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 680rpx;
        z-index: -999;
        image {
            width: 100%;
            height: 100%;
        }
    }

    .navbar {
        .header {
            padding: 30rpx 24rpx;
            color: #fff;
        }
    }
    .user-info {
        padding: 24rpx;
        .user-avatar {
            position: relative;
            .user-status {
                position: absolute;
                bottom: 0;
                right: 0;
                background: #fd0100;
                border-radius: 16rpx 0rpx 16rpx 0rpx;
                padding: 6rpx 12rpx;
            }
        }
    }
    .data-info {
        padding: 24rpx;
        .card {
            padding: 36rpx 30rpx;
            background: #fff;
            border-radius: 12rpx;
            box-shadow: 0rpx 12rpx 12rpx -4rpx rgba(224, 224, 224, 0.28);
            // .data-item {

            // }
            .line {
                width: 2rpx;
                height: 40rpx;
                // margin: 0 24rpx;
                background: #999999;
            }
        }
    }
    .actions-list {
        padding: 0 28rpx;
        .action-item {
            padding: 44rpx 0;
            border-bottom: 1rpx solid #e6e6e6;
        }
    }
}
</style>
