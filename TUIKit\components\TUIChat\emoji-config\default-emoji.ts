/**
 * Emoji input interface in the chat screen.
 * In respect for the copyright of the emoji design, the Chat Demo/TUIKit project does not include the cutouts of large emoji elements.
 * Please replace them with your own designed or copyrighted emoji packs before the official launch for commercial use.
 * The default small yellow face emoji pack is copyrighted by Tencent Cloud and can be authorized for a fee.
 * If you wish to obtain authorization, please submit a ticket to contact us.
 *
 * submit a ticket url：https://console.tencentcloud.com/workorder/category?level1_id=29&level2_id=40&source=14&data_title=Chat&step=1
 */
import { default as emojiCNLocales } from './locales/zh_cn';
import { default as emojiENLocales } from './locales/en';
import { EMOJI_TYPE } from '../../../constant';
import { IEmojiGroupList } from '../../../interface';

export const DEFAULT_BASIC_EMOJI_URL = 'https://web.sdk.qcloud.com/im/assets/emoji-plugin/';
export const DEFAULT_BIG_EMOJI_URL = 'https://web.sdk.qcloud.com/im/assets/face-elem/';

export const DEFAULT_BASIC_EMOJI_URL_MAPPING: Record<string, string> = {
  '[TUIEmoji_Expect]': '<EMAIL>',
  '[TUIEmoji_Blink]': '<EMAIL>',
  '[TUIEmoji_Guffaw]': '<EMAIL>',
  '[TUIEmoji_KindSmile]': '<EMAIL>',
  '[TUIEmoji_Haha]': '<EMAIL>',
  '[TUIEmoji_Cheerful]': '<EMAIL>',
  '[TUIEmoji_Smile]': '<EMAIL>',
  '[TUIEmoji_Sorrow]': '<EMAIL>',
  '[TUIEmoji_Speechless]': '<EMAIL>',
  '[TUIEmoji_Amazed]': '<EMAIL>',
  '[TUIEmoji_Complacent]': '<EMAIL>',
  '[TUIEmoji_Lustful]': '<EMAIL>',
  '[TUIEmoji_Stareyes]': '<EMAIL>',
  '[TUIEmoji_Giggle]': '<EMAIL>',
  '[TUIEmoji_Daemon]': '<EMAIL>',
  '[TUIEmoji_Rage]': '<EMAIL>',
  '[TUIEmoji_Yawn]': '<EMAIL>',
  '[TUIEmoji_TearsLaugh]': '<EMAIL>',
  '[TUIEmoji_Silly]': '<EMAIL>',
  '[TUIEmoji_Wail]': '<EMAIL>',
  '[TUIEmoji_Kiss]': '<EMAIL>',
  '[TUIEmoji_Trapped]': '<EMAIL>',
  '[TUIEmoji_Fear]': '<EMAIL>',
  '[TUIEmoji_BareTeeth]': '<EMAIL>',
  '[TUIEmoji_FlareUp]': '<EMAIL>',
  '[TUIEmoji_Tact]': '<EMAIL>',
  '[TUIEmoji_Shit]': '<EMAIL>',
  '[TUIEmoji_ShutUp]': '<EMAIL>',
  '[TUIEmoji_Sigh]': '<EMAIL>',
  '[TUIEmoji_Hehe]': '<EMAIL>',
  '[TUIEmoji_Silent]': '<EMAIL>',
  '[TUIEmoji_Skull]': '<EMAIL>',
  '[TUIEmoji_Mask]': '<EMAIL>',
  '[TUIEmoji_Beer]': '<EMAIL>',
  '[TUIEmoji_Cake]': '<EMAIL>',
  '[TUIEmoji_RedPacket]': '<EMAIL>',
  '[TUIEmoji_Bombs]': '<EMAIL>',
  '[TUIEmoji_Ai]': '<EMAIL>',
  '[TUIEmoji_Celebrate]': '<EMAIL>',
  '[TUIEmoji_Bless]': '<EMAIL>',
  '[TUIEmoji_Flower]': '<EMAIL>',
  '[TUIEmoji_Watermelon]': '<EMAIL>',
  '[TUIEmoji_Cow]': '<EMAIL>',
  '[TUIEmoji_Fool]': '<EMAIL>',
  '[TUIEmoji_Surprised]': '<EMAIL>',
  '[TUIEmoji_Askance]': '<EMAIL>',
  '[TUIEmoji_Monster]': '<EMAIL>',
  '[TUIEmoji_Pig]': '<EMAIL>',
  '[TUIEmoji_Coffee]': '<EMAIL>',
  '[TUIEmoji_Ok]': '<EMAIL>',
  '[TUIEmoji_Heart]': '<EMAIL>',
  '[TUIEmoji_Sun]': '<EMAIL>',
  '[TUIEmoji_Moon]': '<EMAIL>',
  '[TUIEmoji_Star]': '<EMAIL>',
  '[TUIEmoji_Rich]': '<EMAIL>',
  '[TUIEmoji_Fortune]': '<EMAIL>',
  '[TUIEmoji_857]': '<EMAIL>',
  '[TUIEmoji_666]': '<EMAIL>',
  '[TUIEmoji_Prohibit]': '<EMAIL>',
  '[TUIEmoji_Convinced]': '<EMAIL>',
  '[TUIEmoji_Knife]': '<EMAIL>',
  '[TUIEmoji_Like]': '<EMAIL>',
};

export const BIG_EMOJI_GROUP_LIST: IEmojiGroupList = [
  {
    emojiGroupID: 1,
    type: EMOJI_TYPE.BIG,
    url: DEFAULT_BIG_EMOJI_URL,
    list: ['yz00', 'yz01', 'yz02', 'yz03', 'yz04', 'yz05', 'yz06', 'yz07', 'yz08',
      'yz09', 'yz10', 'yz11', 'yz12', 'yz13', 'yz14', 'yz15', 'yz16', 'yz17'],
  },
  {
    emojiGroupID: 2,
    type: EMOJI_TYPE.BIG,
    url: DEFAULT_BIG_EMOJI_URL,
    list: ['ys00', 'ys01', 'ys02', 'ys03', 'ys04', 'ys05', 'ys06', 'ys07', 'ys08',
      'ys09', 'ys10', 'ys11', 'ys12', 'ys13', 'ys14', 'ys15'],
  },
  {
    emojiGroupID: 3,
    type: EMOJI_TYPE.BIG,
    url: DEFAULT_BIG_EMOJI_URL,
    list: ['gcs00', 'gcs01', 'gcs02', 'gcs03', 'gcs04', 'gcs05', 'gcs06', 'gcs07',
      'gcs08', 'gcs09', 'gcs10', 'gcs11', 'gcs12', 'gcs13', 'gcs14', 'gcs15', 'gcs16'],
  },
];

export const BASIC_EMOJI_NAME_TO_KEY_MAPPING = {
  ...Object.fromEntries(
    Object.entries(emojiCNLocales)?.map(([key, val]) => [val, key]),
  ),
  ...Object.fromEntries(
    Object.entries(emojiENLocales)?.map(([key, val]) => [val, key]),
  ),
};
