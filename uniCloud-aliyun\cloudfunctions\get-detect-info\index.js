'use strict';
const tencentcloud = require("tencentcloud-sdk-nodejs");

// 导入对应产品模块的client models。
const FaceidClient = tencentcloud.faceid.v20180301.Client;

exports.main = async (event, context) => {
    // uni-app客户端上传的参数
    const { bizToken } = event;

    if (!bizToken) {
        return {
            code: 400,
            message: '缺少 bizToken 参数',
        }
    }

    // 重要：腾讯云密钥，请不要硬编码到代码中
    // 建议使用 uniCloud 的 secret.json 或环境变量进行管理
    const clientConfig = {
        credential: {
            secretId: "AKID3ziFZgcLqBNM8f5HI8oJdjgT4RcdYKtI", // TODO: 替换为您的SecretId
            secretKey: "JPHN7i8Q0RgomnfMsBJE1bzGNRTRRIxJ", // TODO: 替换为您的SecretKey
        },
        region: "ap-chengdu", // 地域，请根据您的业务地域进行修改，例如 ap-guangzhou
        profile: {
            httpProfile: {
                endpoint: "faceid.tencentcloudapi.com",
            },
        },
    };

    const client = new FaceidClient(clientConfig);
    const params = {
        "BizToken": bizToken,
        "RuleId": "1" // 根据您的业务需求配置
    };

    try {
        // 调用腾讯云接口
        const data = await client.GetDetectInfoEnhanced(params);
        // 成功后将腾讯云的原始响应返回给客户端
        return data;
    } catch (err) {
        console.error("调用腾讯云API失败", err);
        // 失败后将错误信息返回给客户端
        return {
            code: 500,
            message: '调用腾讯云API失败',
            error: {
                requestId: err.requestId,
                code: err.code,
                message: err.message
            }
        }
    }
}; 