<template>
    <!-- 登录 -->
    <view class="login-page">
        <section class="nav-bar">
            <c-navBar title="登录" isTran isPerch :isBack="false" color="#fff"></c-navBar>
        </section>
        <section class="header-bg">
            <image class="bg-img" src="/static/login/login-bg.png" mode="scaleToFill" />
        </section>
        <section class="tac">
            <!-- 手机号验证码登录 -->
            <view class="form-container" v-if="loginType === 'phone'">
                <view class="mf-font-32 mf-weight-bold" style="text-align: left">账号/手机号</view>
                <view class="input-box">
                    <input type="text" placeholder="请输入手机号" v-model="phoneNumber" />
                </view>

                <view class="input-box code-box">
                    <input type="text" placeholder="请输入验证码" v-model="verifyCode" />
                    <view class="code-btn" @click="getVerifyCode">获取验证码</view>
                </view>

                <!-- <button class="login-btn" @click="phoneLogin">登录</button> -->
                <button class="login-btn flex flex-center mf-weight-bold mf-font-36" @click="testLogin">登录</button>
            </view>

            <!-- 账号密码登录 -->
            <view class="form-container" v-if="loginType === 'account'">
                <view class="mf-font-32 mf-weight-bold" style="text-align: left">账号/手机号</view>
                <view class="input-box">
                    <input type="text" placeholder="请输入账号" v-model="phoneNumber" />
                </view>
                <view class="mf-font-32 mf-weight-bold" style="text-align: left">密码</view>
                <view class="input-box">
                    <input type="password" placeholder="请输入密码" v-model="password" />
                </view>

                <view class="forget-pwd flex justify-between align-center">
                    <!-- <view class="forget-icon flex align-center">
                        <view class="ag-box flex flex-center" @click="tabAgreePass">
                            <image v-if="isAgreePass" src="/static/common/select.png" mode=""></image>
                            <image v-else src="/static/common/noSelect.png" mode=""></image>
                        </view>
                        <view class="forget-text"> 记住密码 </view>
                    </view> -->
                    <!-- <view class="forget-text" @click="$fn.jumpPage('/pages/tabbar/change-pwd', true)"> 忘记密码 </view> -->
                </view>
                <button class="login-btn flex flex-center mf-weight-bold mf-font-36" :class="isDFlag" @click="accountLogin">登录</button>
                <view class="forget-text" @click="handleForgetPwd"> 忘记密码？ </view>
            </view>

            <!-- <view class="account-box">
                <view class="account-login" @click="switchLoginType">
                    {{ loginType === "phone" ? "账号密码登录" : "验证码登录" }}
                </view>
                <view class="no-account">没有账号？<text class="register-text" @click="toRegister">去注册</text></view>
            </view> -->

            <view class="agreement-box flex flex-center">
                <view class="ag-box flex flex-center" @click="tabAgree">
                    <image v-if="isAgree" src="/static/common/select.png" mode=""></image>
                    <image v-else src="/static/common/noSelect.png" mode=""></image>
                </view>
                <view class="agreement">
                    <text @click.stop="tabAgree">我已阅读并同意</text>
                    <!-- <text class="primary-color" @click.stop="jumpXy(1)">《用户协议》</text> -->
                    <text class="primary-color" @click.stop="jumpXy(2)">《隐私协议》</text>
                </view>
            </view>
        </section>
    </view>
</template>

<script>
import { initTUICallKit } from "../../utils/TUICallKitUtils";
export default {
    data() {
        return {
            phoneNumber: "***********",
            verifyCode: "",
            loginType: "account", // 'phone' 或 'account'
            password: "123456",
            tipsShow: false,
            resData: {},
            isAgree: false, //是否同意协议
            isAgreePass: false, //是否记住密码
            needWechatPhone: false, //是否需要微信手机号授权
            isDFlag: "",
        };
    },
    onLoad() {
        this.creatLogin();
    },
    methods: {
        // 切换登录类型
        switchLoginType() {
            this.loginType = this.loginType === "phone" ? "account" : "phone";
            console.log(this.loginType);

            this.isDFlag = this.loginType === "phone" ? "" : "dNone";
        },
        // 忘记密码
        handleForgetPwd() {
            console.log(this.phoneNumber);

            if (!this.phoneNumber) {
                uni.showToast({
                    title: "请输入手机号",
                    icon: "none",
                });
                return;
            }
            // 验证手机号格式
            const phoneReg = /^1[3-9]\d{9}$/;
            if (!phoneReg.test(this.phoneNumber)) {
                uni.showToast({
                    title: "请输入正确的手机号",
                    icon: "none",
                });
                return;
            }
            this.$fn.jumpPage(`/pages/tabbar/change-pwd?phoneNumber=${this.phoneNumber}`);
        },
        // 获取验证码
        async getVerifyCode() {
            if (!this.phoneNumber) {
                uni.showToast({
                    title: "请输入手机号",
                    icon: "none",
                });
                return;
            }
            try {
            } catch (err) {
                console.error(err);
            }
            // 此处调用获取验证码接口
            uni.showToast({
                title: "验证码已发送",
                icon: "none",
            });
        },

        // 手机号登录
        async phoneLogin() {
            if (!this.phoneNumber || !this.verifyCode) {
                uni.showToast({
                    title: "请填写完整信息",
                    icon: "none",
                });
                return;
            }

            if (!this.isAgree) {
                uni.showToast({
                    title: "请先同意用户协议",
                    icon: "none",
                });
                return;
            }
            // 此处调用手机号登录接口
            this.loginSuccess({});
        },

        // 账号密码登录
        async accountLogin() {
            if (!this.phoneNumber || !this.password) {
                uni.showToast({
                    title: "请填写完整信息",
                    icon: "none",
                });
                return;
            }
            // 验证手机号格式
            const phoneReg = /^1[3-9]\d{9}$/;
            if (!phoneReg.test(this.phoneNumber)) {
                uni.showToast({
                    title: "请输入正确的手机号",
                    icon: "none",
                });
                return;
            }
            if (!this.isAgree) {
                uni.showToast({
                    title: "请先同意用户协议",
                    icon: "none",
                });
                return;
            }
            uni.showLoading({
                title: "登录中...",
            });
            try {
                const res = await this.$api.loginByPassword({
                    mobile: this.phoneNumber,
                    password: this.password,
                });
                if (res.code == 200) {
                    await this.handleGetMyCenter();
                    this.loginSuccess();
                }
            } catch (err) {
                console.error(err);
                this.$fn.showToast("网络出了一点小问题");
            }
            uni.hideLoading();
        },
        // 获取个人中心数据
        async handleGetMyCenter() {
            try {
                const res = await this.$api.getMyCenter();
                if (res.code === 200) {
                    this.userInfo = res.data;
                    uni.setStorageSync("userInfo", this.userInfo);
                    // 新增：检查用户登录状态，如果已登录，则调用初始化函数
                    // #ifdef APP-PLUS
                    if (this.userInfo && this.userInfo.id) {
                        console.log("App.vue: 检测到用户已登录，开始初始化IM及音视频服务...");
                        await initTUICallKit();
                    } else {
                        console.log("App.vue: 用户未登录，跳过初始化。");
                    }
                    // #endif
                }
            } catch (error) {
                console.log(error);
                this.$fn.showToast("获取个人中心数据失败");
            }
        },
        // 去注册页面
        toRegister() {
            uni.navigateTo({
                url: "/pages/tabbar/register",
            });
        },

        // 跳转协议
        jumpXy(e) {
            uni.navigateTo({
                url: "/pages/tabbar/agreement?type=" + e,
            });
        },
        // 同意/不同意协议
        tabAgree() {
            this.isAgree = !this.isAgree;
        },
        tabAgreePass() {
            this.isAgreePass = !this.isAgreePass;
        },
        // 初始登录获取openid
        creatLogin() {
            // 初始化登录相关代码
        },
        // 未同意协议
        notAgreed() {
            uni.showToast({
                title: "请先同意用户协议",
                icon: "none",
                duration: 2000,
            });
        },
        // 登录成功
        async loginSuccess() {
            uni.showToast({
                title: "登录成功！",
                icon: "none",
                duration: 2000,
            });

            setTimeout(function () {
                uni.switchTab({
                    url: "/pages/tabbar/home",
                    fail(error) {
                        console.log(error);
                    },
                });
            }, 1000);
        },
        // 忘记密码
        forgetPassword() {
            uni.navigateTo({
                url: "/pages/tabbar/change-pwd",
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.login-page {
    position: relative;

    .header-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -999;

        .bg-img {
            width: 100%;
            height: 800rpx;
        }
    }

    .tac {
        margin-top: 330rpx;
        background: #fff;
        border-radius: 32rpx 32rpx 0 0;
        z-index: 999;
        padding: 48rpx;

        .form-container {
            .input-box {
                margin-top: 12rpx;
                height: 96rpx;
                border-bottom: 1rpx solid #e5e5e5;
                margin-bottom: 48rpx;
                display: flex;
                align-items: center;

                input {
                    height: 100%;
                    width: 100%;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #8c9299;
                    text-align: left;
                }

                &.code-box {
                    display: flex;
                    justify-content: space-between;

                    .code-btn {
                        padding-left: 20rpx;
                        font-weight: 500;
                        font-size: 28rpx;
                        color: #ec3015;
                    }
                }
            }

            .login-btn {
                margin-top: 80rpx;
                width: 100%;
                height: 96rpx;
                background: #fd0100;
                border-radius: 12rpx;
                color: #ffffff;
                margin-bottom: 32rpx;

                &.dNone {
                    margin-top: 112rpx;
                }
            }

            .forget-text {
                text-align: center;
                font-weight: 400;
                font-size: 24rpx;
                color: #fd0100;
            }

            .forget-pwd {
                position: absolute;
                bottom: 136rpx;
                width: 590rpx;

                .forget-icon {
                    .ag-box {
                        width: 50rpx;
                        height: 50rpx;

                        image {
                            width: 24rpx;
                            height: 24rpx;
                        }
                    }
                }
            }
        }
    }
}

.agreement-box {
    position: fixed;
    bottom: 82rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;

    .ag-box {
        width: 50rpx;
        height: 50rpx;

        image {
            width: 24rpx;
            height: 24rpx;
        }
    }

    .agreement {
        font-size: 28rpx;
        font-weight: 500;
        color: #666666;

        .primary-color {
            color: #e6212a;
        }
    }
}
</style>
