<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch :title="title" :isBack="true"> </c-navBar>
        </section>
        <section class="content">
            <u-parse :content="content"></u-parse>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            title: "",
            content: "",
        };
    },
    onLoad(options) {
        this.title = options.title;
        this.handleGetSystemConfig(options.type);
    },

    methods: {
        // 获取系统配置
        async handleGetSystemConfig(type) {
            try {
                const res = await this.$api.getSystemConfig({ type });
                if (res.code === 200) {
                    this.content = res.data;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取系统配置失败");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    .content {
        padding: 24rpx;
    }
}
</style>
