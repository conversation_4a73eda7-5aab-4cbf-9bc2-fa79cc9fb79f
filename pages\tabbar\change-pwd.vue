<template>
    <!-- 更换手机号-验证原手机号 -->
    <view class="container">
        <c-navBar title="忘记密码" isSeat isPerch :isBack="true"></c-navBar>
        <view class="content">
            <view class="title">短信验证</view>

            <view class="phone-info">
                <text>需向您手机号</text>
                <text class="phone-number">{{ phoneNumber }}</text>
                <text>发送短信验证码以验证身份。</text>
            </view>

            <view class="input-box">
                <input type="text" placeholder="请输入验证码" v-model="verifyCode" />
                <view class="code-btn" @click="getVerifyCode">{{ isCounting ? `${countdown}s` : "发送验证码" }}</view>
            </view>
            <view class="btn-box">
                <button class="next-btn" @click="goToNewPwd">下一步</button>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            verifyCode: "",
            countdown: 60,
            isCounting: false,
            phoneNumber: "",
        };
    },
    onLoad(options) {
        const userInfo = uni.getStorageSync("userInfo");
        this.phoneNumber = options.phoneNumber || userInfo.mobile;
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },

        // 获取验证码
        async getVerifyCode() {
            try {
                if (this.isCounting) return;
                if (!this.phoneNumber) {
                    uni.showToast({
                        title: "请输入手机号",
                        icon: "none",
                    });
                    return;
                }

                const res = await this.$api.sendCode({ mobile: this.phoneNumber });
                console.log(res);
                if (res.code === 200) {
                    // 开始倒计时
                    this.isCounting = true;
                    this.countdown = 60;
                    const timer = setInterval(() => {
                        this.countdown--;
                        if (this.countdown <= 0) {
                            this.isCounting = false;
                            clearInterval(timer);
                        }
                    }, 1000);
                }
            } catch (err) {
                console.error(err);
            }
        },

        // 前往设置新密码页面
        goToNewPwd() {
            if (!this.verifyCode) {
                uni.showToast({
                    title: "请输入验证码",
                    icon: "none",
                });
                return;
            }
            this.$fn.jumpPage(`/pages/tabbar/changePass?code=${this.verifyCode}&mobile=${this.phoneNumber}`);
        },
    },
};
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    background-color: #fff;
}

.content {
    padding: 32rpx;
}

.title {
    font-weight: bold;
    font-size: 32rpx;
    color: #000000;
    margin-bottom: 24rpx;
}

.phone-info {
    font-weight: 400;
    font-size: 24rpx;
    color: #858585;
    margin-bottom: 44rpx;
    .phone-number {
        color: #3d3d3d;
        padding: 0 10rpx;
    }
}

.input-box {
    height: 100rpx;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 80rpx;

    input {
        flex: 1;
        height: 100%;
        font-size: 24rpx;
        color: #999999;
    }

    .code-btn {
        font-size: 28rpx;
        white-space: nowrap;
        color: #ec3015;
    }
}
.btn-box {
    position: fixed;
    padding: 20rpx 32rpx 68rpx 32rpx;
    padding-bottom: env(safe-area-inset-bottom);
    background: #fff;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    z-index: 999;
}
.next-btn {
    width: 686rpx;
    height: 96rpx;
    background: linear-gradient(315deg, #ec3015 0%, #ff0036 100%);
    border-radius: 12rpx;
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 500;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
