.transfer-h5 {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  &-wechat {
    width: 100vw;
    height: 100vh;
  }

  &-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    padding: 16px 18px;

    .space, .icon {
      width: 18px;
      height: 18px;
    }
  }

  .main {
    flex: 1;
    flex-direction: column;
    width: auto;
    height: auto;
    border-radius: 0;
    border: none;
    box-shadow: none;
    max-height: calc(100% - 50px);
    padding: 0;

    .avatar {
      border-radius: 5px;
    }

    .left {
      padding: 0;
      flex: 1;
      border: none;
      display: flex;
      flex-direction: column;

      .transfer-header {
        position: sticky;
        top: 0;
        padding: 0 18px;

        input {
          border-radius: 5px;
          font-size: 14px;
        }
      }

      &-uniapp-input {
        height: 36px;
      }
    }

    .right {
      flex: 0;
      flex-direction: row;
      align-items: center;
      box-shadow: inset 0 1px 0 0 #EEE;
      padding: 8px 18px ;

      .transfer-list {
        flex-direction: row;
        width: 0;

        &-item {
          &-content {
            flex: none;
          }
        }
      }

      .transfer-right-footer {
        padding: 6px 0;
        display: flex;
        align-items: center;

        .btn {
          font-size: 14px;
        }
      }
    }
  }
}
