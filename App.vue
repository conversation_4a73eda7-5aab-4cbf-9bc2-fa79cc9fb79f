<script>
// 不再需要由App.vue主动创建TUIKit实例
// import TUIKit from './TUIKit';
import { initTUICallKit } from "@/utils/TUICallKitUtils.js";
import { initTencentCloudPush } from "@/utils/TencentCloudPush.js";
export default {
    onLaunch: function (options) {
        console.log("App Launch");
        // 监听IM就绪事件
        uni.$on("im-ready", ({ tim, TIM }) => {
            console.log("IM IS READY. Attaching message listener.");
            // 将 tim 实例挂载到全局
            this.globalData.tim = tim;
            this.globalData.TIM = TIM;
            Vue.prototype.tim = tim;
            Vue.prototype.TIM = TIM;

            // 监听新消息，用于实现应用在后台时的本地推送
            tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived);
        });
        // #ifdef APP-PLUS
        // 检查和请求通知权限
        this.checkAndRequestPushPermission();
        // #endif
    },
    async onShow() {
        console.log("App Show");
        this.globalData.isAppInForeground = true;
        // 进入前台时，清除角标
        // #ifdef APP-PLUS
        plus.runtime.setBadgeNumber(0);
        try {
            const promise = uni.$chat.isReady();
            if (promise) {
                console.log("IM是否就绪", promise);
            } else {
                await initTUICallKit(); // 腾讯云音视频初始化
                await initTencentCloudPush(); // 腾讯云推送初始化
            }
        } catch (error) {
            console.error("IM就绪失败", error);
            await initTUICallKit(); // 腾讯云音视频初始化
            await initTencentCloudPush(); // 腾讯云推送初始化
        }
        // #endif
    },
    onHide() {
        console.log("App Hide");
    },
    onExit() {
        console.log("App Exit");
        this.handleLogout();
        this.globalData.isAppInForeground = false;
    },
    globalData: {
        userID: "",
        userSig: "",
        token: "",
        expiresIn: "",
        phone: "",
        userInfo: {},
        isAppInForeground: true, // 应用是否在前台
    },
    methods: {
        // 退出登录
        async handleLogout() {
            try {
                // #ifdef APP-PLUS
                uni.$TUICallKit.logout((res) => {
                    if (res.code === 0) {
                        console.log("音视频退出成功", res);
                    } else {
                        console.error(res);
                    }
                });
                uni.$TUILogin.logout((res) => {
                    if (res.code === 0) {
                        console.log("IM退出成功", res);
                    } else {
                        console.error(res);
                    }
                });
                // #endif
            } catch (error) {
                console.error("IM等退出登录时出错", error);
                // this.$fn.showToast("退出登录时出错");
            }
        },
        // #ifdef APP-PLUS
        checkAndRequestPushPermission() {
            // Android 13+ 需要动态请求 POST_NOTIFICATIONS 权限
            plus.android.requestPermissions(
                [
                    "android.permission.POST_NOTIFICATIONS",
                    "android.permission.CAMERA"
                ],
                (e) => {
                    let notificationGranted = false;
                    let cameraGranted = false;
                    if (e.granted && Array.isArray(e.granted)) {
                        notificationGranted = e.granted.includes("android.permission.POST_NOTIFICATIONS");
                        cameraGranted = e.granted.includes("android.permission.CAMERA");
                    } else if (e.granted) {
                        // 兼容部分机型返回为true
                        notificationGranted = true;
                        cameraGranted = true;
                    }
                    if (notificationGranted && cameraGranted) {
                        console.log("通知权限和摄像头权限已获取");
                    } else {
                        let msg = "";
                        if (!notificationGranted) {
                            msg += "您拒绝了通知权限，将无法在后台收到新消息提醒。\n";
                        }
                        if (!cameraGranted) {
                            msg += "您拒绝了摄像头权限，部分功能将无法使用。";
                        }
                        uni.showModal({
                            title: "提示",
                            content: msg,
                            showCancel: false,
                        });
                    }
                },
                (e) => {
                    console.error("请求通知或摄像头权限失败:", e);
                }
            );
        },
        // #endif
    },
};
</script>

<style lang="scss">
/*每个页面公共css */
@import "uni_modules/uview-ui/index.scss";
@import "./common/css/commonStyle.scss";
page {
    overscroll-behavior-y: none;
    font-family: "FZXiaoBiaoSong-B05S", "FZXiaoBiaoSong-B05S";
}

@font-face {
    font-family: "FZXiaoBiaoSong-B05S";
    src: url("http://ds.mufengweilai.com/static/fonts/fzxbsjw.ttf") format("ttf");
}

.securityBc {
    width: 100%;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #fff;
}

.securityZone {
    width: 100%;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
}

body {
    // background-color: #f5f5f5;
    position: relative;
    z-index: -5;
    font-family: "FZXiaoBiaoSong-B05S", "FZXiaoBiaoSong-B05S";
}
</style>
