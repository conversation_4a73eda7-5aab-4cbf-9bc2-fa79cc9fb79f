<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isTran isPerch title="张三三" backgroundColor="#FD0100" isBack color="#fff" showRight>
                <view slot="right">
                    <view class="mf-font-28" style="color: #fff">举报投诉</view>
                </view>
            </c-navBar>
        </section>
        <scroll-view
            scroll-y
            class="message-list"
            :scroll-top="scrollTop"
            :scroll-with-animation="true"
            :scroll-anchoring="true"
            @scrolltoupper="loadMore"
            upper-threshold="50"
            :style="{ height: `calc(100vh - 330rpx)` }"
        >
            <view class="message-container flex flex-col gap-30" id="message-container">
                <!-- 使用v-for动态渲染消息列表 -->
                <view
                    v-for="(item, index) in messageList"
                    :key="index"
                    :class="['message-item', item.type === 'send' ? 'right-message' : 'left-message']"
                >
                    <view class="avatar" v-if="item.type === 'receive'">
                        <image src="/static/mine/avatar.png" style="width: 80rpx; height: 80rpx" mode="aspectFill"></image>
                    </view>

                    <view :class="['message-content', item.type === 'send' ? 'right' : 'left']">
                        <view class="message-text">{{ item.content }}</view>
                        <view class="message-time">{{ item.time }}</view>
                    </view>
                    <view class="avatar" v-if="item.type === 'send'">
                        <image src="/static/mine/avatar.png" style="width: 80rpx; height: 80rpx" mode="aspectFill"></image>
                    </view>
                </view>
                <view id="bottom-anchor"></view>
            </view>
        </scroll-view>
        <section class="footer">
            <view class="actions flex align-center gap-16">
                <u-image src="/static/common/message1.png" width="56rpx" height="56rpx" mode="aspectFill"></u-image>
                <u-input
                    ref="messageInput"
                    v-model="message"
                    placeholder="请输入消息"
                    confirmType="send"
                    @confirm="sendMessage"
                    :focus="inputFocus"
                    :customStyle="{
                        border: '2rpx solid #FFDFDF',
                        borderRadius: '8rpx',
                        background: '#FFFFFF',
                        height: '72rpx',
                        padding: '0 12rpx',
                        flex: 1,
                    }"
                    border="none"
                />
                <u-image src="/static/common/message2.png" width="56rpx" height="56rpx" mode="aspectFill" @click="sendMessage"></u-image>

                <u-image src="/static/common/message3.png" width="56rpx" height="56rpx" mode="aspectFill"></u-image>

                <!-- <u-button size="mini" type="primary" @click="sendMessage" style="background-color: #FD0100; margin-left: 10rpx;">发送</u-button> -->
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            message: "",
            scrollTop: 0,
            inputFocus: false,
            messageList: [
                {
                    id: 1,
                    type: "receive", // 接收的消息
                    content: "你好你好，你好，我需要请假",
                    time: "2023-03-21 09:30",
                },
                {
                    id: 2,
                    type: "send", // 发送的消息
                    content: "好的，请问什么时候请假？请假多久？",
                    time: "2023-03-21 09:31",
                },
                {
                    id: 3,
                    type: "receive",
                    content: "我想请下周一到周三的假，有家里的事情需要处理",
                    time: "2023-03-21 09:32",
                },
                {
                    id: 4,
                    type: "send",
                    content: "收到，我会帮你安排的",
                    time: "2023-03-21 09:33",
                },
            ],
        };
    },
    mounted() {
        // 初始滚动到底部
        this.$nextTick(() => {
            this.scrollToBottom();
        });
    },
    methods: {
        // 加载更多历史消息
        loadMore() {
            // 加载更多历史消息的逻辑
            console.log("到顶部了，可以加载更多历史消息");
        },
        // 滚动到底部
        scrollToBottom() {
            // 使用uni-app的createSelectorQuery来获取元素
            const query = uni.createSelectorQuery().in(this);
            query
                .select("#message-container")
                .boundingClientRect((data) => {
                    if (data) {
                        // 强制更新视图
                        this.$nextTick(() => {
                            this.scrollTop = data.height;
                        });
                    }
                })
                .exec();
        },
        // 聚焦到输入框
        focusInput() {
            // 聚焦到输入框
            this.inputFocus = true;
            // 由于uni-app的focus属性可能需要先设置为false再设置为true才能重新聚焦
            this.$nextTick(() => {
                this.inputFocus = false;
                setTimeout(() => {
                    this.inputFocus = true;
                }, 50);
            });
        },
        // 发送消息
        sendMessage() {
            if (!this.message.trim()) return;

            this.messageList.push({
                id: this.messageList.length + 1,
                type: "send",
                content: this.message,
                time: new Date().toLocaleString(),
            });

            this.message = "";

            // 滚动到底部
            this.$nextTick(() => {
                this.scrollToBottom();
                // 发送消息后聚焦到输入框
                this.focusInput();
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    min-height: 100vh;
    background: #fbfbfb;

    .message-list {
        padding: 20rpx;
        box-sizing: border-box;

        .message-container {

            .message-item {
                display: flex;
                margin-bottom: 20rpx;

                &.left-message {
                    align-self: flex-start;
                }

                &.right-message {
                    align-self: flex-end;
                    justify-content: flex-end;
                }

                .avatar {
                    margin: 0 20rpx;
                }

                .message-content {
                    max-width: 70%;

                    &.left {
                        background-color: #ffffff;
                        border-radius: 20rpx;
                        padding: 20rpx;
                        position: relative;
                        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
                    }

                    &.right {
                        background-color: #fd0100;
                        border-radius: 20rpx;
                        padding: 20rpx;
                        position: relative;

                        .message-text {
                            color: #ffffff;
                            word-wrap: break-word;
                            word-break: break-all;
                            white-space: pre-wrap;
                        }

                        .message-time {
                            color: rgba(255, 255, 255, 0.8);
                            font-size: 20rpx;
                            text-align: right;
                            margin-top: 10rpx;
                        }
                    }

                    .message-text {
                        word-wrap: break-word;
                        word-break: break-all;
                        white-space: pre-wrap;
                    }

                    .message-time {
                        font-size: 20rpx;
                        color: #999;
                        margin-top: 10rpx;
                        text-align: right;
                    }
                }
            }
        }
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff5f5;
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }
}
</style>
