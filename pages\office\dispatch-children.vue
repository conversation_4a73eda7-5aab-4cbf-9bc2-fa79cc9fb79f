<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="调度" :isBack="true" color="#000"> </c-navBar>
        </section>
        <section class="groups">
            <view v-if="currentPath.length > 0" class="back-item flex align-center" @click="goBack">
                <u-icon name="arrow-left" size="32rpx" style="margin-right: 16rpx"></u-icon>
                <text class="mf-font-32">返回上级</text>
            </view>
            <!-- 市驻村办 -->
            <view
                class="group-item flex align-center gap-24"
                v-if="data.framework_label === '市驻村办'"
                v-for="item in data.user"
                :key="item.id"
            >
                <view class="user flex-1 flex align-center justify-between" @click="handleCheck(item)">
                    <view class="flex align-center gap-24">
                        <view class="user-avatar">
                            <u-image
                                :src="item.avatar || '/static/common/group-icon2.png'"
                                width="128rpx"
                                height="128rpx"
                                radius="16rpx"
                                mode="scaleToFill "
                            ></u-image>
                            <view class="user-status mf-font-20" style="color: #fff">{{ item.online_status.name }}</view>
                        </view>
                        <view class="flex flex-col gap-30">
                            <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ item.nickname }}</view>
                            <view class="mf-font-28" style="color: #ebb84c">{{ item.role_name }}</view>
                        </view>
                    </view>
                    <view class="checkbox">
                        <view class="uncheck-box" v-if="!isChecked(item)"></view>
                        <view class="check-box" v-else></view>
                    </view>
                </view>
            </view>
            <!-- 派出单位 -->
            <view
                class="group-item flex align-center gap-24"
                v-if="data.framework_label === '派出单位'"
                v-for="item in data.user"
                :key="item.id"
            >
                <view class="user flex-1 flex align-center justify-between" @click="handleCheck(item)">
                    <view class="flex align-center gap-24">
                        <view class="user-avatar">
                            <u-image
                                :src="item.avatar || '/static/common/group-icon2.png'"
                                width="128rpx"
                                height="128rpx"
                                radius="16rpx"
                                mode="scaleToFill "
                            ></u-image>
                            <view class="user-status mf-font-20" style="color: #fff">{{ item.online_status.name }}</view>
                        </view>
                        <view class="flex flex-col gap-30">
                            <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ item.nickname }}</view>
                            <view class="mf-font-28" style="color: #ebb84c">{{ item.role_name || "驻村队长" }}</view>
                        </view>
                    </view>
                    <view class="checkbox">
                        <view class="uncheck-box" v-if="!isChecked(item)"></view>
                        <view class="check-box" v-else></view>
                    </view>
                </view>
            </view>
            <!-- 乡镇/下钻通用 -->
            <view v-if="currentLevelList.length > 0">
                <view class="group-item flex align-center gap-24" v-for="(item, index1) in currentLevelList" :key="index1">
                    <!-- group类型 -->
                    <view v-if="item.user" class="group" @click="goToChildren(item)">
                        <u-image
                            src="/static/common/group-icon2.png"
                            width="72rpx"
                            height="72rpx"
                            radius="8rpx"
                            mode="aspectFill"
                        ></u-image>
                        <view class="flex align-center justify-between flex-1">
                            <text class="mf-font-32 mf-weight-bold">{{ item.framework_label || item.name || "加载中..." }}</text>
                            <u-icon name="arrow-right" size="32rpx"></u-icon>
                        </view>
                    </view>
                    <!-- user类型 -->
                    <view v-else class="user flex-1 flex align-center justify-between" @click="handleCheck(item)">
                        <view class="flex align-center gap-24">
                            <view class="user-avatar">
                                <u-image
                                    :src="item.avatar || '/static/mine/avatar.png'"
                                    width="128rpx"
                                    height="128rpx"
                                    radius="16rpx"
                                    mode="scaleToFill"
                                ></u-image>
                                <view class="user-status mf-font-20" style="color: #fff">{{ item.online_status.name }}</view>
                            </view>
                            <view class="flex flex-col gap-30">
                                <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ item.nickname }}</view>
                                <view class="mf-font-28" style="color: #ebb84c">{{ item.role_name || item.role || "加载中..." }}</view>
                            </view>
                        </view>
                        <view class="checkbox">
                            <view class="uncheck-box" v-if="!isChecked(item)"></view>
                            <view class="check-box" v-else></view>
                        </view>
                    </view>
                </view>
            </view>
        </section>
        <section class="footer">
            <view
                v-if="hasUser"
                class="btn flex flex-center mf-font-36 mf-weight-bold"
                :style="checkedUser ? 'color: #fff; background: #fd0100;' : 'color: #fff; background: #fd0100; opacity: 0.5; pointer-events: none;'"
                @click="submitDispatch"
            >
                发起调度
            </view>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            api: () => {},
            apiParams: {},
            checkedUser: null, // 单选
            currentPath: [], // 记录层级
            currentLevelList: [], // 当前层级列表
            data: {}, // 组织结构数据
        };
    },
    onLoad(options) {
        this.$nextTick(() => {
            // 兼容传参，支持data结构
            if (options && options.data) {
                this.data = JSON.parse(options.data);
                console.log(this.data);
                this.currentLevelList = this.data.role_group || [];
            } else {
                this.currentLevelList = [];
            }
        });
    },
    computed: {
        hasUser() {
            // 市驻村办/派出单位
            if (this.data.framework_label === '市驻村办' || this.data.framework_label === '派出单位') {
                return Array.isArray(this.data.user) && this.data.user.length > 0;
            }
            // 乡镇/下钻通用
            if (Array.isArray(this.currentLevelList)) {
                return this.currentLevelList.some(item => !item.user);
            }
            return false;
        }
    },
    methods: {
        // 检查是否选中
        isChecked(item) {
            return this.checkedUser && this.checkedUser.id === item.id;
        },
        // 处理选中/取消选中
        handleCheck(item) {
            if (this.checkedUser && this.checkedUser.id === item.id) {
                this.checkedUser = null;
            } else {
                this.checkedUser = item;
            }
        },
        // 进入下一级
        goToChildren(role) {
            if (role.user && role.user.length > 0) {
                this.currentPath.push(this.currentLevelList); // 记录当前层级
                this.currentLevelList = role.user; // 进入下一级
            }
        },
        // 返回上一级
        goBack() {
            if (this.currentPath.length > 0) {
                this.currentLevelList = this.currentPath.pop();
            }
        },
        // 发起调度
        submitDispatch() {
            if (!this.checkedUser) {
                return; // 按钮置灰时不执行
            }
            this.$fn.jumpPage("/pages/office/initiateScheduling");
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    background: #fbfbfb;
    min-height: 100vh;
    padding-bottom: 150rpx;

    .groups {
        .back-item {
            padding: 30rpx 32rpx;
            background: #fff;
            margin-bottom: 10rpx;
            border-radius: 12rpx;
        }

        .group-item {
            padding: 40rpx 32rpx;
            border-radius: 12rpx;
            background: #fff;
            margin-bottom: 10rpx;
            &:last-child {
                margin-bottom: 0;
            }

            .group {
                display: flex;
                align-items: center;
                gap: 24rpx;
                width: 100%;
            }

            .user {
                .user-avatar {
                    position: relative;
                    .user-status {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        background: #fd0100;
                        border-radius: 16rpx 0rpx 16rpx 0rpx;
                        padding: 4rpx 8rpx;
                    }
                }
                .checkbox {
                    .uncheck-box {
                        width: 44rpx;
                        height: 44rpx;
                        border-radius: 8rpx;
                        border: 1rpx solid #000;
                        background: #e6e6e6;
                    }
                    .check-box {
                        width: 44rpx;
                        height: 44rpx;
                        border-radius: 4rpx;
                        background: #fd0100;
                        position: relative;
                        &::before {
                            content: "";
                            position: absolute;
                            top: 8rpx;
                            left: 6rpx;
                            transform: rotate(-45deg);
                            width: 65%;
                            height: 30%;
                            border-radius: 4rpx;
                            border-bottom: 4rpx solid #fff;
                            border-left: 4rpx solid #fff;
                        }
                    }
                }
            }
        }
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: #fbfbfb;
        padding: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
        .btn {
            width: 100%;
            height: 100%;
            background: #fd0100;
            border-radius: 12rpx;
        }
    }
}
</style>
