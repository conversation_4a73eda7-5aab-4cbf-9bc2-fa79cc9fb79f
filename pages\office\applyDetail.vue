<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isPerch title="审批详情" :isBack="true"></c-navBar>
        </section>
        <section class="status mf-font-32 mf-weight-bold" v-if="info.status">
            <view
                v-for="item in statusList"
                v-if="info.status.value === item.value"
                :key="item.value"
                class="status-item flex flex-center"
                :style="item.style">
                {{ info.status.name }}
            </view>
        </section>
        <section class="info">
            <view class="info-item" v-for="item in info.data" :key="item.label">
                <view
                    class="flex align-center justify-between"
                    v-if="item.type === 'text'"
                    @click="item.lable == '分配司机' ? (showDriverPicke = true) : ''">
                    <view class="info-item-label mf-font-28 mf-weight-bold" style="color: #333">{{ item.label }}</view>
                    <view class="info-item-value mf-font-28" :style="{ color: item.color || '#999' }">{{ item.value }}</view>
                </view>
                <view class="flex flex-col gap-24" v-if="item.type === 'textarea'">
                    <view class="info-item-label mf-font-28 mf-weight-bold" style="color: #333">{{ item.label }}</view>
                    <view class="info-item-value mf-font-28" style="color: #999">{{ item.value }}</view>
                </view>
                <view class="flex flex-col gap-24" v-if="item.type === 'img'">
                    <view class="info-item-label mf-font-28 mf-weight-bold" style="color: #333">{{ item.label }}</view>
                    <view class="info-item-value mf-font-28 flex flex-wrap gap-24" style="color: #999" v-if="item.value.length > 0">
                        <u-image
                            :src="img"
                            width="200rpx"
                            height="200rpx"
                            radius="8rpx"
                            v-for="(img, index) in item.value"
                            :key="index"></u-image>
                    </view>
                    <view class="info-item-value mf-font-28" style="color: #999" v-else>--</view>
                </view>
            </view>
        </section>
        <section class="line"></section>
        <section class="progress" v-if="type !== '资金绩效'">
            <view class="progress-title mf-font-32 mf-weight-bold" style="color: #1a1a1a">审批流程</view>
            <view class="steps">
                <view class="step-item flex align-start gap-24" v-for="(item, idx) in approveList" :key="idx">
                    <view class="avatar">
                        <u-image
                            :src="item.approver ? item.approver.avatar : '/static/mine/avatar.png'"
                            width="48rpx"
                            height="48rpx"
                            radius="8rpx"
                            mode="aspectFill"></u-image>
                    </view>
                    <view class="step-item-content flex flex-col gap-12">
                        <view class="flex align-end step-item-title gap-16">
                            <text class="mf-font-28 mf-weight-bold" style="color: #1a1a1a">
                                {{ item.approver ? item.approver.nickname : "获取失败" }}
                            </text>
                            <text class="mf-font-24" style="color: #666">{{ item.approverRole ? item.approverRole.role_name : "--" }}</text>
                        </view>
                        <view
                            class="mf-font-20"
                            :style="{
                                color:
                                    item.status && item.status.value === 0
                                        ? '#ebb84c'
                                        : item.status && item.status.value === 1
                                        ? '#3198ff'
                                        : item.status && item.status.value === 2
                                        ? '#fd0100'
                                        : '#999',
                            }">
                            {{ item.status && item.status.name ? item.status.name : "--" }}
                        </view>
                        <view class="step-item-desc mf-font-20" style="color: #999">
                            {{ item.approve_time || "-" }}
                        </view>
                    </view>
                </view>
                <view class="line"></view>
            </view>
        </section>
        <section class="footer flex flex-center" v-if="isSelf">
            <view
                class="btn-item flex-1 flex flex-center"
                style="background: #eeeef0; color: #666666; border-radius: 12rpx 0 0 12rpx"
                @click="confirmRefuseApply">
                <text class="mf-font-36 mf-weight-bold"> 驳回 </text>
            </view>
            <view
                class="btn-item flex-1 flex flex-center"
                style="background: #fd0100; color: #ffffff; border-radius: 0 12rpx 12rpx 0"
                @click="confirmAgreeApply">
                <text class="mf-font-36 mf-weight-bold"> 同意 </text>
            </view>
        </section>
        <!-- 从审核完成列表点击进入,车辆派遣 -->
        <section
            class="footer flex flex-center"
            v-if="
                (type == '外出报备' || type == '请假') && dispatch && originData.car_info && Object.keys(originData.car_info).length == 0
            ">
            <view
                class="btn-item flex-1 flex flex-center"
                style="background: #fd0100; color: #ffffff; border-radius: 12rpx"
                @click="showDriverPicker = true">
                <text class="mf-font-36 mf-weight-bold">车辆派遣</text>
            </view>
        </section>
        <!-- 这里是调试信息，分别对应：申请类型|接口申请人|本地申请人昵称|是否存在车辆信息|是否为本人发起|行程状态|是否已确认行程 -->
        <!-- {{ type }}|{{ this.originData.apply_user }}|{{ userInfo.nickname }}|{{
            originData.car_info && Object.keys(originData.car_info).length > 0
        }}|{{ isSelfApply }}|{{ status }}|
        {{ itineraryDetails && itineraryDetails.is_confirm }} -->
        <!-- 已分配司机 -->
        <section
            class="footer flex flex-center"
            v-if="
                (type == '外出报备' || type == '请假') && originData.car_info && Object.keys(originData.car_info).length > 0 && isSelfApply
            ">
            <view
                class="btn-item flex-1 flex flex-center"
                v-if="!is_confirm && status == 1"
                style="background: #fd0100; color: #ffffff; border-radius: 12rpx"
                @click="handleUseCar('confirm')">
                <text class="mf-font-36 mf-weight-bold">确认行程</text>
            </view>
            <view
                class="btn-item flex-1 flex flex-center"
                v-if="is_confirm && status == 1"
                style="background: #fd0100; color: #ffffff; border-radius: 12rpx"
                @click="handleUseCar('arrive')">
                <text class="mf-font-36 mf-weight-bold">开始行程</text>
            </view>
            <view
                class="btn-item flex-1 flex flex-center"
                v-if="is_confirm && status == 2"
                style="background: #fd0100; color: #ffffff; border-radius: 12rpx"
                @click="handleUseCar('receive')">
                <text class="mf-font-36 mf-weight-bold">行程结束</text>
            </view>
        </section>
        <!-- 销假/续假 -->
        <section
            class="footer flex flex-center"
            v-if="type == '请假' && status == 3 && isSelfApply && (originData.clear_button || originData.continue_button)">
            <view
                class="btn-item flex-1 flex flex-center"
                v-if="originData.clear_button"
                :style="{
                    background: originData.clear_button ? originData.clear_button.color : '#fd0100',
                    color: '#ffffff',
                    borderRadius: '12rpx 0 0 12rpx',
                }"
                @click="confirmClearLeave">
                <text class="mf-font-36 mf-weight-bold">{{ originData.clear_button ? originData.clear_button.name : "销假" }}</text>
            </view>
            <view
                class="btn-item flex-1 flex flex-center"
                v-if="originData.continue_button"
                :style="{
                    background: originData.continue_button ? originData.continue_button.color : '#fd0100',
                    color: '#ffffff',
                    borderRadius: originData.clear_button ? '0 12rpx 12rpx 0' : '12rpx',
                }"
                @click="showContinueLeavePicker = true">
                <text class="mf-font-36 mf-weight-bold">{{ originData.continue_button ? originData.continue_button.name : "续假" }}</text>
            </view>
        </section>
        <u-popup :show="showRefusePopup" mode="center" round="16rpx" :safeAreaInsetBottom="false">
            <view class="refuse-popup">
                <view class="refuse-popup-title mf-font-32 mf-weight-bold">请假驳回</view>
                <view class="refuse-popup-content">
                    <u-textarea
                        v-model="form.refuse_reason"
                        placeholder="请输入驳回原因"
                        :maxlength="80"
                        :auto-height="false"
                        count
                        height="180"
                        :border="false"
                        :custom-style="{ 'background-color': '#f7f7f7' }"></u-textarea>
                </view>
                <view class="refuse-popup-footer flex">
                    <view class="refuse-popup-btn refuse-popup-btn-cancel" @click="cancelRefuse">取消</view>
                    <view class="refuse-popup-btn refuse-popup-btn-confirm" @click="submitRefuse">提交</view>
                </view>
            </view>
        </u-popup>
        <!-- 司机选择 -->
        <u-picker
            :show="showDriverPicker"
            :columns="[driver]"
            keyName="label"
            @confirm="handleConfirmDriverPicker"
            @close="showDriverPicker = false"
            @cancel="showDriverPicker = false"
            close-on-click-overlay></u-picker>
        <!-- 续假结束时间选择 -->
        <u-datetime-picker
            :show="showContinueLeavePicker"
            v-model="continueLeaveDate"
            @cancel="showContinueLeavePicker = false"
            @close="showContinueLeavePicker = false"
            close-on-click-overlay
            :minDate="minDate"
            mode="date"
            @confirm="handleContinueLeaveConfirm"></u-datetime-picker>
    </view>
</template>

<script>
import { startFaceVerify } from "../../common/js/cloudIai.js";
export default {
    data() {
        return {
            minDate: Number(new Date()), // 续假结束时间最小值
            continueLeaveDate: "", // 续假结束时间
            showContinueLeavePicker: false, // 续假结束时间选择
            showRefusePopup: false, // 驳回弹窗
            showDriverPicker: false, // 司机选择弹窗
            driver: [], // 司机列表
            chooseDriver: {}, // 选择司机
            info: {
                // 审批详情
                status: {}, // 状态
                data: [], // 数据
            },
            dispatch: false, // 是否可进行车辆派遣(完成审核-单独入口-由vehicleDispatch.vue页面进入时传入,当前页面不修改)
            originData: {}, // 原始数据 // 保留做处理
            userInfo: {}, // 用户信息
            itineraryDetails: null, // 行程详情
            approveList: [], // 审批流程
            statusList: [
                // 状态列表
                { value: 0, style: "background: #fff9ef; color: #ebb84c" },
                { value: 1, style: "background: #e9f4ff; color: #4798f2" },
                { value: 2, style: "background: #ffeeed; color: #fa3231" },
            ],
            form: {
                // 表单
                id: "", // 申请id
                sign_image: "", // 签名图片
                car_id: "", // 车辆id
                refuse_reason: "", // 拒绝原因
            },
            isSelf: false, // 是否轮到本人审核(当前审批流程中是否轮到本人审核)
            isSelfApply: false, // 当前审核项目是否为本人发起(当前审批流程中是否为本人发起)
            is_confirm: false, // 当前乘客已是否确认行程
            status: 1, // 当前行程状态 1:未开始 2:已开始 3:已结束
            type: "", // 申请类型
        };
    },
    computed: {},
    async onLoad(options) {
        this.form.id = options.id;
        this.type = options.type;
        this.dispatch = options.dispatch;
        this.userInfo = uni.getStorageSync("userInfo");
        if (this.type === "请假") {
            await this.handleGetLeaveDetail(options.id);
        } else if (this.type === "外出报备") {
            await this.handleGetOutReportInfo(options.id);
        } else if (this.type === "补卡") {
            await this.handleGetReclockDetail(options.id);
        } else if (this.type === "资金绩效") {
            await this.handleGetFundsDetail(options.id);
        }
        await this.handleGetCarList();
    },
    onShow() {
        if (uni.getStorageSync("faceRecognitionSuccess")) {
            const isFace = uni.getStorageSync("faceRecognitionSuccess");
            uni.removeStorageSync("faceRecognitionSuccess");
            if (isFace === "ok") {
                this.handleClearLeave();
            } else {
                this.$fn.showToast("人脸认证失败,请重试");
            }
        }
    },
    methods: {
        // 续假结束时间选择
        async handleContinueLeaveConfirm(e) {
            console.log("续假结束时间选择", this.$u.timeFormat(e.value, "yyyy-mm-dd"));
            try {
                const res = await this.$api.continueLeave({ id: this.form.id, end_date: this.$u.timeFormat(e.value, "yyyy-mm-dd") });
                if (res.code === 200) {
                    this.$fn.showToast("续假成功");
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1000);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("续假失败");
            }
        },
        // 确认销假
        confirmClearLeave() {
            uni.showModal({
                title: "提示",
                content: "确定销假吗？",
                success: async (res) => {
                    if (res.confirm) {
                        try {
                            this.$fn.showToast("正在启动人脸核身...");
                            const ruleId = "1";
                            await startFaceVerify(ruleId);
                        } catch (error) {
                            console.error(error);
                            this.$fn.showToast("启动人脸核身失败");
                        }
                    }
                },
            });
        },
        // 销假
        async handleClearLeave() {
            try {
                const location = await this.$fn.getLocation();
                console.log(location.latitude, location.longitude);
                const res = await this.$api.clearLeave({ id: this.form.id, lat: location.latitude, lng: location.longitude });
                if (res.code === 200) {
                    this.$fn.showToast("销假成功");
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1000);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("销假失败");
            }
        },
        // 处理行程操作
        async handleUseCar(action) {
            try {
                // 判断是否存在车辆信息 不存在则不进行操作，(判断是否已经派了车)
                if (!this.originData.car_info || Object.keys(this.originData.car_info).length == 0) return;
                const data = {
                    id: this.form.id, // 申请id
                    action: action, // 查询:search, 确认行程:confirm, 已上车:receive, 到达:arrive
                };
                // 判断行程状态
                const car_info = this.originData.car_info;
                if (Object.keys(car_info).length > 0 && this.is_confirm && this.status == 1) {
                    // 为1时未开始，进行上车操作，进入状态已开始
                    data.action = "receive"; // 已上车
                } else if (Object.keys(car_info).length > 0 && this.is_confirm && this.status == 2) {
                    // 为2时已开始，进行到达操作3，进入状态已结束
                    data.action = "arrive"; // 到达
                }
                const res = await this.$api.handleCar(data);
                if (res.code === 200) {
                    console.log("车辆行程操作成功", res);
                    this.itineraryDetails = res.data;
                    this.is_confirm = res.data.is_confirm == 1 ? true : false;
                    this.status = res.data.status;
                    console.log("行程状态", this.status);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("车辆行程操作失败");
            }
        },
        // 确认司机
        handleConfirmDriverPicker(item) {
            if (!this.dispatch) {
                this.$fn.showToast("无车辆派遣权限");
                return;
            }
            this.chooseDriver = item.value[0];
            this.form.car_id = item.value[0].value;
            uni.showModal({
                title: "提示",
                content: "确定分配该司机吗？",
                success: (res) => {
                    if (res.confirm) {
                        this.showDriverPicker = false;
                        console.log(this.form);
                        this.handleSetCar();
                    }
                },
            });
        },
        // 获取司机列表
        async handleGetCarList() {
            try {
                const res = await this.$api.getCarList({ page: 1 });
                if (res.code === 200) {
                    this.driver = res.data.list.map((item) => ({ label: `${item.driver_name}(${item.status_tag.name})`, value: item.id }));
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取司机列表失败");
            }
        },
        // 图片上传
        handleUploadImage(image, callback) {
            uni.showLoading({
                title: "上传中...",
            });
            uni.uploadFile({
                url: this.vuex_baseUrl + "/common/upload",
                filePath: image,
                name: "file",
                success: (res) => {
                    callback(res);
                },
                fail: (err) => {
                    console.error("图片上传失败", err);
                    this.$fn.showToast("签名图片上传失败");
                },
                complete: () => {
                    uni.hideLoading();
                },
            });
        },
        // 确认同意申请
        confirmAgreeApply() {
            uni.navigateTo({
                url: `/pages/office/sign`,
                events: {
                    // 监听签名完成事件
                    // 当sign.vue页面调用 eventChannel.emit('onSignComplete', ...) 时触发
                    onSignComplete: (data) => {
                        console.log("签名完成，图片路径：", data.signaturePath);
                        // 在这里处理获取到的签名图片路径，例如上传到服务器或在当前页面显示
                        this.handleUploadImage(data.signaturePath, (res) => {
                            const data = JSON.parse(res.data);
                            this.form.sign_image = data.data.url;
                            console.log(this.form.sign_image);

                            uni.showModal({
                                title: "提示",
                                content: "确定同意申请吗？",
                                success: (res) => {
                                    if (res.confirm) {
                                        this.handleAgreeApply();
                                    }
                                },
                            });
                        });
                    },
                },
                fail(err) {
                    console.error("跳转到签名页失败", err);
                },
            });
        },
        // 确认拒绝申请
        confirmRefuseApply() {
            this.showRefusePopup = true;
            console.log(this.showRefusePopup);
        },
        // 取消拒绝申请
        cancelRefuse() {
            this.showRefusePopup = false;
            this.form.refuse_reason = "";
        },
        // 提交拒绝申请
        submitRefuse() {
            if (!this.form.refuse_reason.trim()) {
                return this.$fn.showToast("请输入驳回原因");
            }
            this.handleRefuseApply();
        },
        // 分配司机
        async handleSetCar() {
            try {
                const res = await this.$api.setCar({ id: this.form.id, car_id: this.form.car_id });
                if (res.code === 200) {
                    this.$fn.showToast("分配司机成功");
                    this.info.data = [];
                    if (this.type === "请假") {
                        this.handleGetLeaveDetail(this.form.id);
                    } else if (this.type === "外出报备") {
                        this.handleGetOutReportInfo(this.form.id);
                    }
                    this.showDriverPicker = false;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("分配司机失败");
            }
        },
        // 同意申请
        async handleAgreeApply() {
            try {
                const res = await this.$api.agreeApply(this.form);
                if (res.code === 200) {
                    this.$fn.showToast("同意申请成功");
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1000);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("同意申请失败");
            }
        },
        // 拒绝申请
        async handleRefuseApply() {
            try {
                const res = await this.$api.refuseApply({ id: this.form.id, refuse_reason: this.form.refuse_reason });
                if (res.code === 200) {
                    this.$fn.showToast("驳回成功");
                    this.cancelRefuse();
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1000);
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("拒绝申请失败");
            }
        },
        // 获取请假详情
        async handleGetLeaveDetail(id) {
            try {
                const res = await this.$api.leaveDetail({ id });
                if (res.code === 200) {
                    this.originData = res.data;
                    console.log("请假详情", this.originData);
                    this.handleData(res.data);
                    // 按approve_step从小到大排序
                    this.approveList = (res.data.approve_flow || []).slice().sort((a, b) => {
                        return Number(a.approve_step) - Number(b.approve_step);
                    });
                    const approveSelf = this.originData; // 审批详情
                    this.isSelf = approveSelf.can_approve === 1; // 是否轮到本人审核
                    this.isSelfApply = approveSelf.LeaveUser.nickname == this.userInfo.nickname; // 当前审核项目是否为本人发起
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取请假详情失败");
            }
        },
        // 获取外出详情
        async handleGetOutReportInfo(id) {
            try {
                const res = await this.$api.outReportDetail({ id });
                if (res.code === 200) {
                    this.originData = res.data;
                    console.log("外出详情", this.originData);
                    this.handleData(res.data);
                    // 按approve_step从小到大排序
                    this.approveList = (res.data.approve_flow || []).slice().sort((a, b) => {
                        return Number(a.approve_step) - Number(b.approve_step);
                    });
                    const approveSelf = this.originData;
                    this.isSelf = approveSelf.can_approve === 1;
                    this.isSelfApply = approveSelf.ApplyUser.nickname == this.userInfo.nickname;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取外出详情失败");
            }
        },
        // 获取补卡详情
        async handleGetReclockDetail(id) {
            try {
                const res = await this.$api.reclockDetail({ id });
                if (res.code === 200) {
                    this.originData = res.data;
                    this.handleData(res.data);
                    // 按approve_step从小到大排序
                    this.approveList = (res.data.approve_flow || []).slice().sort((a, b) => {
                        return Number(a.approve_step) - Number(b.approve_step);
                    });
                    const approveSelf = this.originData; // 审批详情
                    this.isSelf = approveSelf.can_approve === 1; // 是否轮到本人审核
                    this.isSelfApply = approveSelf.ApplyUser.nickname == this.userInfo.nickname; // 当前审核项目是否为本人发起
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取补卡详情失败");
            }
        },
        // 获取资金绩效详情
        async handleGetFundsDetail(id) {
            try {
                const res = await this.$api.fundsDetail({ id });
                if (res.code === 200) {
                    this.originData = res.data;
                    this.handleData(res.data);
                    const approveSelf = this.originData; // 审批详情
                    this.isSelf = approveSelf.can_approve === 1; // 是否轮到本人审核
                    this.isSelfApply = approveSelf.ApplyUser.nickname == this.userInfo.nickname; // 当前审核项目是否为本人发起
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("获取资金绩效详情失败");
            }
        },
        // 数据处理
        async handleData(data) {
            if (!this.$fn.isEmptyObject(data.car_info)) {
                await this.handleUseCar("search");
            }
            this.info.status = data.approve_status;
            // 设置统一数据
            if (this.type !== "资金绩效") {
                this.info.data.push({
                    label: "审批标题",
                    value: data.title,
                    type: "text",
                });
                this.info.data.push({
                    label: "所在单位",
                    value: data.group_name,
                    type: "text",
                });
                this.info.data.push({
                    label: "职位",
                    value: data.role_name,
                    type: "text",
                });
            }
            // 补卡
            if (this.type == "补卡") {
                this.info.data.push({
                    label: "补卡时间",
                    value: data.clock_date,
                    type: "text",
                });
                this.info.data.push({
                    label: "补卡原因",
                    value: data.clock_reason,
                    type: "text",
                });
            }
            // 请假
            if (this.type == "请假") {
                this.info.data.push({
                    label: "开始日期",
                    value: data.start_date,
                    type: "text",
                });
                this.info.data.push({
                    label: "结束日期",
                    value: data.end_date,
                    type: "text",
                });
                this.info.data.push({
                    label: "离开时间",
                    value: data.go_time,
                    type: "text",
                });
                this.info.data.push({
                    label: "请假时间",
                    value: `${data.day_num}天`,
                    type: "text",
                });
                this.info.data.push({
                    label: "请假类别",
                    value: data.leave_type.name,
                    type: "text",
                });
                this.info.data.push({
                    label: "请假原因",
                    value: data.leave_reason,
                    type: "text",
                });
                this.info.data.push({
                    label: "到达地点",
                    value: data.destination,
                    type: "text",
                });
                if (this.dispatch) {
                    this.info.data.push({
                        label: "分配司机",
                        value: data.car_info && data.car_info.driver_name ? `${data.car_info.driver_name}-${data.car_info.car_no}` : "--",
                        type: "text",
                    });
                }
                console.log("this.itineraryDetails", this.itineraryDetails);

                if (!this.$fn.isEmptyObject(data.car_info) && this.itineraryDetails) {
                    this.info.data.push({
                        label: "司机电话",
                        value: data.car_info && data.car_info.driver_mobile ? `${data.car_info.driver_mobile}` : "--",
                        type: "text",
                    });
                    this.info.data.push({
                        label: "行程状态",
                        value: this.itineraryDetails.status_tag.name,
                        color: this.itineraryDetails.status_tag.color,
                        type: "text",
                    });
                } else {
                    this.info.data.push({
                        label: "行程状态",
                        value: "待派车",
                        type: "text",
                    });
                }
                this.info.data.push({
                    label: "图片",
                    value: data.images,
                    type: "img",
                });
            }
            // 外出报备
            if (this.type == "外出报备") {
                this.info.data.push({
                    label: "开始时间",
                    value: data.start_date,
                    type: "text",
                });
                this.info.data.push({
                    label: "结束时间",
                    value: data.end_date,
                    type: "text",
                });
                this.info.data.push({
                    label: "外出时间",
                    value: data.day_num,
                    type: "text",
                });
                this.info.data.push({
                    label: "外出原因",
                    value: data.reason,
                    type: "text",
                });
                if (this.dispatch) {
                    this.info.data.push({
                        label: "分配司机",
                        value: data.car_info && data.car_info.driver_name ? `${data.car_info.driver_name}-${data.car_info.car_no}` : "--",
                        type: "text",
                    });
                }
                if (Object.keys(data.car_info).length > 0 && this.itineraryDetails) {
                    this.info.data.push({
                        label: "司机电话",
                        value: data.car_info && data.car_info.driver_mobile ? `${data.car_info.driver_mobile}` : "--",
                        type: "text",
                    });
                    this.info.data.push({
                        label: "行程状态",
                        value: this.itineraryDetails.status_tag.name,
                        color: this.itineraryDetails.status_tag.color,
                        type: "text",
                    });
                } else {
                    this.info.data.push({
                        label: "行程状态",
                        value: "待派车",
                        type: "text",
                    });
                }
            }
            // 资金绩效
            if (this.type == "资金绩效") {
                this.info.data.push({
                    label: "经费类别",
                    value: data.funds_type.name,
                    type: "text",
                });
                this.info.data.push({
                    label: "项目总金额",
                    value: data.total_amount,
                    type: "text",
                });
                this.info.data.push({
                    label: "已支出金额",
                    value: data.use_amount,
                    type: "text",
                });
                this.info.data.push({
                    label: "效益及分红机制",
                    value: data.project_benefit,
                    type: "text",
                });
                this.info.data.push({
                    label: "项目内容",
                    value: data.project_content,
                    type: "text",
                });
                this.info.data.push({
                    label: "项目进度",
                    value: data.project_progress,
                    type: "text",
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    // background: #f2f4f7;
    padding-bottom: calc(env(safe-area-inset-bottom) + 140rpx);

    .status {
        .status-item {
            padding: 22rpx 0;
        }
    }

    .info {
        padding: 0 24rpx;
        background: #fff;

        .info-item {
            padding: 32rpx 0;
            border-bottom: 1rpx solid #e5e5e5;

            &:last-child {
                border-bottom: none;
            }
        }
    }

    .line {
        height: 10rpx;
        background: #f2f4f7;
    }

    .progress {
        background: #fff;
        padding: 32rpx 24rpx;
        z-index: 888;

        .steps {
            margin-top: 24rpx;
            position: relative;

            .step-item {
                margin-top: 64rpx;

                .avatar {
                    z-index: 777;
                }

                &:first-child {
                    margin-top: 0;
                }
            }

            .line {
                position: absolute;
                top: 0;
                left: 24rpx;
                width: 2rpx;
                height: 90%;
                border-left: 2rpx dashed #c8cacc;
                z-index: 666;
            }
        }
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100rpx;
        background: #fff;
        padding: 24rpx;
        padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
        z-index: 999;

        .btn-item {
            height: 96rpx;
        }
    }
}

.refuse-popup {
    background-color: #fff;
    width: 624rpx;
    border-radius: 16rpx;

    .refuse-popup-title {
        text-align: center;
        padding: 16rpx 0;
        color: #333;
    }

    .refuse-popup-content {
        padding: 0 32rpx;
    }

    .refuse-popup-counter {
        position: absolute;
        bottom: 10rpx;
        right: 20rpx;
        font-size: 24rpx;
        color: #999;
    }

    .refuse-popup-footer {
        border-top: 1rpx solid #d9d9d9;
        margin-top: 32rpx;

        .refuse-popup-btn {
            flex: 1;
            text-align: center;
            padding: 24rpx 0;
            font-size: 32rpx;
            font-weight: 500;
            color: #fd0100;
        }

        .refuse-popup-btn-cancel {
            border-right: 1rpx solid #d9d9d9;
        }

        .refuse-popup-btn-confirm {
            font-weight: bold;
        }
    }
}
</style>
