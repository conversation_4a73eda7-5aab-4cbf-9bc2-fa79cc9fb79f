'use strict';
const https = require("https")
const crypto = require("crypto")
const config = require("./config.json")

function sha256(message, secret = "", encoding) {
	const hmac = crypto.createHmac("sha256", secret)
	return hmac.update(message).digest(encoding)
}

function getHash(message, encoding = "hex") {
	const hash = crypto.createHash("sha256")
	return hash.update(message).digest(encoding)
}

function getDate(timestamp) {
	const date = new Date(timestamp * 1000)
	const year = date.getUTCFullYear()
	const month = ("0" + (date.getUTCMonth() + 1)).slice(-2)
	const day = ("0" + date.getUTCDate()).slice(-2)
	return `${year}-${month}-${day}`
}

exports.main = async (event, context) => {
	const {
		payload
	} = event

	if (!payload) {
		return {
			errCode: 'INVALID_PAYLOAD',
			errMsg: 'Payload is required.'
		}
	}
	
	const SECRET_ID = config.TENCENTCLOUD_SECRET_ID
	const SECRET_KEY = config.TENCENTCLOUD_SECRET_KEY
	
	if (!SECRET_ID || !SECRET_KEY) {
		return {
			errCode: 'MISSING_SECRET',
			errMsg: 'SecretId or SecretKey is not configured in cloud function config.json.'
		}
	}

	const TOKEN = ""
	const host = "iai.tencentcloudapi.com"
	const service = "iai"
	const region = "ap-chengdu"
	const action = "SearchFaces"
	const version = "2020-03-03"

	return new Promise((resolve, reject) => {
		const timestamp = parseInt(String(new Date().getTime() / 1000))
		const date = getDate(timestamp)
		const payloadString = JSON.stringify(payload)

		// ************* 步骤 1：拼接规范请求串 *************
		const signedHeaders = "content-type;host"
		const hashedRequestPayload = getHash(payloadString)
		const httpRequestMethod = "POST"
		const canonicalUri = "/"
		const canonicalQueryString = ""
		const canonicalHeaders =
			"content-type:application/json; charset=utf-8\n" + "host:" + host + "\n"

		const canonicalRequest =
			httpRequestMethod +
			"\n" +
			canonicalUri +
			"\n" +
			canonicalQueryString +
			"\n" +
			canonicalHeaders +
			"\n" +
			signedHeaders +
			"\n" +
			hashedRequestPayload

		// ************* 步骤 2：拼接待签名字符串 *************
		const algorithm = "TC3-HMAC-SHA256"
		const hashedCanonicalRequest = getHash(canonicalRequest)
		const credentialScope = date + "/" + service + "/" + "tc3_request"
		const stringToSign =
			algorithm +
			"\n" +
			timestamp +
			"\n" +
			credentialScope +
			"\n" +
			hashedCanonicalRequest

		// ************* 步骤 3：计算签名 *************
		const kDate = sha256(date, "TC3" + SECRET_KEY)
		const kService = sha256(service, kDate)
		const kSigning = sha256("tc3_request", kService)
		const signature = sha256(stringToSign, kSigning, "hex")

		// ************* 步骤 4：拼接 Authorization *************
		const authorization =
			algorithm +
			" " +
			"Credential=" +
			SECRET_ID +
			"/" +
			credentialScope +
			", " +
			"SignedHeaders=" +
			signedHeaders +
			", " +
			"Signature=" +
			signature

		// ************* 步骤 5：构造并发起请求 *************
		const headers = {
			Authorization: authorization,
			"Content-Type": "application/json; charset=utf-8",
			Host: host,
			"X-TC-Action": action,
			"X-TC-Timestamp": timestamp,
			"X-TC-Version": version,
		}

		if (region) {
			headers["X-TC-Region"] = region
		}
		if (TOKEN) {
			headers["X-TC-Token"] = TOKEN
		}

		const options = {
			hostname: host,
			method: httpRequestMethod,
			headers,
		}

		const req = https.request(options, (res) => {
			let data = ""
			res.on("data", (chunk) => {
				data += chunk
			})

			res.on("end", () => {
				// The result from Tencent Cloud is a JSON string, parse it before returning
				try {
					resolve(JSON.parse(data));
				} catch (e) {
					resolve({
						errCode: 'PARSE_ERROR',
						errMsg: 'Failed to parse response from Tencent Cloud API',
						originalData: data
					})
				}
			})
		})

		req.on("error", (error) => {
			console.error(error)
			resolve({ // Resolve instead of reject to get structured error on client
				errCode: 'REQUEST_ERROR',
				errMsg: error.message
			})
		})

		req.write(payloadString)
		req.end()
	})
}; 