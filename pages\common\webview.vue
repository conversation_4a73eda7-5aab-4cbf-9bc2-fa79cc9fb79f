<template>
    <view>
        <web-view :src="webviewSrc" @message="handleMessage"></web-view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            webviewSrc: ''
        };
    },
    onLoad(options) {
        if (options && options.url) {
            this.webviewSrc = decodeURIComponent(options.url);
            console.log('WebView loaded with URL:', this.webviewSrc);
        } else {
            console.error('No URL provided to webview');
            uni.showToast({
                title: '页面链接无效',
                icon: 'none'
            });
        }
    },
    methods: {
        handleMessage(e) {
            const biztoken = e.detail.data[0].biztoken;
            console.log("收到返回的biztoken:", biztoken);

            if (!biztoken) {
                uni.showToast({
                    title: '人脸识别凭证无效',
                    icon: 'none'
                });
                return;
            }
            uniCloud.callFunction({
                name: 'get-detect-info',
                data: {
                    bizToken: biztoken
                }
            }).then(res => {
                if (res.result && res.result.RequestId) {
                    console.log('获取人脸识别结果成功:', res.result.Text);
                    if (res.result.Text.ErrMsg == '成功') {
                        uni.setStorageSync('faceRecognitionSuccess', 'ok');
                    } else {
                        uni.setStorageSync('faceRecognitionSuccess', 'error');
                    }
                    uni.switchTab({
                        url: '/pages/tabbar/home'
                    });

                } else {
                    console.error('获取人脸识别结果失败:', res.result);
                }
            }).catch(err => {
                console.error('云函数调用失败:', err);
            });
        }
    }
};
</script>

<style></style>