<template>
    <view class="view">
        <section class="navbar">
            <c-navBar isTran isPerch isBack title="通讯录" color="#000"> </c-navBar>
        </section>
        <section class="search">
            <view class="search-box">
                <u-input
                    placeholder="搜索"
                    confirmType="search"
                    color="#000"
                    border="none"
                    input-align="center"
                    v-model="keywords"
                    @confirm="handleSearch" />
            </view>
        </section>
        <section class="groups" v-if="list.length > 0">
            <view class="group-item flex align-center gap-24" v-for="item in list" :key="item.id">
                <view class="user flex-1 flex align-center justify-between" @click="handleCheck(item)">
                    <view class="flex align-center gap-24">
                        <view class="user-avatar">
                            <u-image
                                :src="item.avatar || '/static/mine/avatar.png'"
                                width="128rpx"
                                height="128rpx"
                                radius="16rpx"
                                mode="scaleToFill "></u-image>
                            <view class="user-status mf-font-20" style="color: #fff">{{ item.online_status.name }}</view>
                        </view>
                        <view class="flex flex-col gap-30">
                            <view class="mf-font-32 mf-weight-bold" style="color: #1a1a1a">{{ item.nickname }}</view>
                            <view class="mf-font-28" style="color: #ebb84c">{{ item.role.role_name }}</view>
                        </view>
                    </view>
                    <view class="action-box">
                        <view class="btn mf-font-28" style="color: #1a1a1a" @click.stop="handleClickMessage(item)"> 发私信 </view>
                    </view>
                </view>
            </view>
        </section>
        <section class="empty" v-else>
            <u-empty mode="search" icon="search" />
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            keywords: "",
            list: [],
        };
    },
    methods: {
        async handleSearch() {
            try {
                const res = await this.$api.searchUser({ keywords: this.keywords });
                if (res.code === 200) {
                    this.list = res.data.list;
                }
            } catch (error) {
                console.error(error);
                this.$fn.showToast("搜索失败");
            }
        },
        // 发送私信
        async handleClickMessage(item) {
            // #ifndef APP-PLUS
            this.$fn.showToast("请前往APP使用");
            return;
            // #endif
            try {
                // 切换到 public001 群会话
                console.log(uni.$chat);
                const promise = await uni.$chat.getConversationProfile(`C2C${item.id}`);
                console.log("会话查找成功", promise);
                this.$fn.jumpPage(`/TUIKit/components/TUIChat/index?conversationID=C2C${item.id}`);
            } catch (error) {
                console.error("切换群会话失败", error);

                uni.showModal({
                    title: "切换群会话失败",
                    content: `对方未登陆过该系统或未上线：${error.message}`,
                    showCancel: false,
                    confirmText: "确定",
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.view {
    position: relative;
    min-height: 100vh;
    .search {
        padding: 24rpx;

        .search-box {
            padding: 20rpx 0;
            border-radius: 12rpx;
            background: #f7f8fa;
        }
    }
    .groups {
        padding: 24rpx;
        .group-item {
            // border-radius: 12rpx;
            padding: 36rpx 0;
            background: #fff;
            border-bottom: 1rpx solid #e6e6e6;
            &:first-child {
                padding-top: 0;
            }
            .user {
                .user-avatar {
                    position: relative;
                    .user-status {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        background: #fd0100;
                        border-radius: 16rpx 0rpx 16rpx 0rpx;
                        padding: 4rpx 8rpx;
                    }
                }
                .action-box {
                    .btn {
                        padding: 16rpx 22rpx;
                        border-radius: 8rpx;
                        border: 1rpx solid #e6e6e6;
                    }
                }
            }
        }
    }
}
</style>
